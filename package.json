{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon src/server.js --host", "prisma:gen": "prisma db pull && prisma generate", "prisma:seed": "prisma db seed", "prisma:admin-seed": "node prisma/admin-seed.js", "prisma:chains-seed": "node prisma/popular-chains-seed.js", "prisma:dev": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.10.1", "axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "ethers": "^6.15.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-useragent": "^1.0.15", "i18n": "^0.15.1", "joi": "^17.13.3", "moment": "^2.30.1", "mongodb": "^6.17.0", "multer": "^2.0.1", "node-cron": "^4.1.1", "prisma": "^6.10.1", "request-ip": "^3.3.0", "sharp": "^0.34.2", "twitter-api-v2": "^1.24.0", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"nodemon": "^3.1.10"}}