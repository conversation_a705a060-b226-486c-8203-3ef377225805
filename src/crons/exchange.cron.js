const config = require("../configs/custom.config");
const cron = require("node-cron");
const exchangeJob = require('../jobs/exchange.job');
const ExchangeManager = require('../services/ExchangeManager');

exports.start = async (prisma) => {
    const manager = new ExchangeManager(prisma);
    await manager.initialize().catch(console.error);
    if (config.isDebug) {
        await manager.syncMarkets().catch(console.error);
        await manager.syncMarketData().catch(console.error);
        await manager.syncRawData().catch(console.error);
        await manager.syncDepth().catch(console.error);
        await manager.calculateAvgLiquidity().catch(console.error);
        await exchangeJob.rankCoinsByTradingVolume(prisma).catch(console.error);
    }
    cron.schedule('0 0 * * *', async () => {
        await manager.syncMarkets().catch(console.error);
        await manager.calculateAvgLiquidity().catch(console.error);
    });
    cron.schedule('*/5 * * * *', async () => {
        await manager.syncMarketData().catch(console.error);
        await manager.syncRawData().catch(console.error);
        await manager.syncDepth().catch(console.error);
        await exchangeJob.rankCoinsByTradingVolume(prisma).catch(console.error);
    });
}