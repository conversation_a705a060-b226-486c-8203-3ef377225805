const config = require("../configs/custom.config");
const cron = require("node-cron");
const coinJob = require("../jobs/coin.job");

exports.start = async (prisma) => {
    if (config.isDebug) {
        await coinJob.getCoinData(prisma);
        await coinJob.generateTrendingCoin(prisma);
        await coinJob.rankCoinsByMarketCap(prisma);
    }
    cron.schedule('*/5 * * * *', async () => {
        await coinJob.getCoinData(prisma);
        await coinJob.generateTrendingCoin(prisma);
        await coinJob.rankCoinsByMarketCap(prisma);
    });
}