const cron = require('node-cron');
const dexDataJob = require('../jobs/dexData.job');
const config = require("../configs/custom.config");

exports.start = async (prisma) => {
    if (config.isDebug) {
        dexDataJob.syncDexPairs(prisma).catch(console.error);
        dexDataJob.updateDexPairStats(prisma).catch(console.error);
    }

    cron.schedule('0 */6 * * *', async () => {
        await dexDataJob.syncDexPairs(prisma);
    });

    cron.schedule('*/5 * * * *', async () => {
        await dexDataJob.updateDexPairStats(prisma);
    });

    // Sync OHLCV data every hour
    cron.schedule('0 * * * *', async () => {
        // console.log('🔄 Syncing DEX OHLCV data...');
        // await dexDataJob.syncDexOHLCV(prisma);
    });
}