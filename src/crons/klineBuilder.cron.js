const cron = require("node-cron");
const kLineBuilderJob = require("../jobs/klineBuilder.job");

exports.start = async (prisma) => {
    cron.schedule('*/5 * * * *', async () => {
        kLineBuilderJob.startKLineBuildJob('5M', 5, prisma).then();
        kLineBuilderJob.startKLineBuildJob('15M', 15, prisma).then();
        kLineBuilderJob.startKLineBuildJob('1H', 60, prisma).then();
        kLineBuilderJob.startKLineBuildJob('24H', 1440, prisma).then();
    });
}