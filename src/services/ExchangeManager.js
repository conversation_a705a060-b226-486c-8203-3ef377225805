const ExchangeFactory = require('./exchang/ExchangeFactory');
const coinStatsModel = require("../models/coinStats.model");
const exchangeStatsModel = require("../models/exchangeStats.model");
const pairStatsModel = require("../models/pairStats.model");
const priceHistoricalModel = require("../models/priceHistorical.model");

class ExchangeManager {
    constructor(PrismaClient) {
        this.prisma = PrismaClient;
        this.exchangesConfig = new Map();
    }

    async initialize() {
        const exchangeConfigs = await this.prisma.exchanges.findMany({
            where: {is_active: true},
            select: {
                id: true,
                name: true,
                exchange_metadata: {
                    select: {
                        api_base_endpoint: true,
                        market_endpoint: true,
                        ticker_endpoint: true,
                        kline_endpoint: true,
                        pair_format: true,
                        trade_endpoint: true,
                        depth_endpoint: true,
                    }
                }
            }
        });

        const configs = exchangeConfigs.map((con) => ({
            id: con.id,
            name: con.name,
            apiEndpoint: con.exchange_metadata.api_base_endpoint,
            marketsEndpoint: con.exchange_metadata.market_endpoint,
            tickersEndpoint: con.exchange_metadata.ticker_endpoint,
            klineEndpoint: con.exchange_metadata.kline_endpoint,
            tradingPairFormat: con.exchange_metadata.pair_format,
            tradeEndpoint: con.exchange_metadata.trade_endpoint,
            depthEndpoint: con.exchange_metadata.depth_endpoint,
        }));

        for (const config of configs) {
            const exchange = ExchangeFactory.createExchange(config);
            this.exchangesConfig.set(config.id, exchange);
        }
    }

    async syncMarkets() {
        for (const [exchangeId, exchange] of this.exchangesConfig) {
            try {
                const markets = await exchange.fetchMarkets();
                await this.updateMarkets(exchangeId, markets);
            } catch (error) {
                console.error(`Failed to sync markets for ${exchange.name}:`, error);
            }
        }
    }

    async syncMarketData() {
        for (const [exchangeId, exchange] of this.exchangesConfig) {
            try {
                const marketData = await exchange.fetchMarketData();
                await this.updateMarketData(exchangeId, marketData);
            } catch (error) {
                console.error(`Failed to sync market data for ${exchange.name}:`, error);
            }
        }
    }

    async syncHistoricalData(exchangeName, coinID, interval) {
        for (const [exchangeId, exchange] of this.exchangesConfig) {
            if (exchange.name === exchangeName) {
                try {
                    let markets = await this.prisma.markets.findMany({
                        where: {
                            exchange_id: exchangeId,
                            is_active: true,
                            base_token_id: coinID,
                        },
                        select: {
                            pair: true,
                            base_token_id: true,
                        }
                    });
                    for (const market of markets) {
                        const coin = await this.prisma.coins.findFirst({
                            where: {
                                id: market.base_token_id,
                                is_active: true,
                            },
                            select: {
                                id: true,
                                launch_date: true,
                            }
                        });
                        if (coin) {
                            const data = await exchange.fetchHistoricalData(market.pair, coin.launch_date);
                            console.log(`Data length == ${data.length}`);
                            const finalData = data.map((i) => ({
                                date_time: i.timestamp.toISOString(),
                                open: i.open,
                                close: i.close,
                                high: i.high,
                                low: i.low,
                                volume: i.volume,
                                market_cap: i.close,
                            }));
                            console.log(`Data length == ${finalData.length}`);
                            await priceHistoricalModel.insertKLineDataBulk(finalData, coin.id, interval);
                            console.log(`Set Historical Data For Exchange ${exchange.name}, Pair ${market.pair}, CoinID ${coin.id}`);
                            return true;
                        } else {
                            console.log(`Skip Fetching Historical Data For Exchange ${exchange.name}, Pair ${market.pair}`);
                        }
                    }
                    console.log('Complete Fetching Historical Data');
                } catch (error) {
                    console.error(`Failed to sync historical data for ${exchange.name}:`, error);
                }
            }
        }
        return false;
    }

    async syncRawData() {
        for (const [exchangeId, exchange] of this.exchangesConfig) {
            try {
                const markets = await this.prisma.markets.findMany({
                    where: {
                        exchange_id: exchangeId,
                    },
                    select: {
                        id: true,
                        exchange_id: true,
                        base_token_id: true,
                        pair: true,
                    }
                })
                const symbols = markets.map((i) => (i.pair));
                const rawData = await exchange.fetchRawData(symbols);
                await this.processRawData(rawData, markets);
            } catch (error) {
                console.error(`Failed to sync raw data for ${exchange.name}:`, error);
            }
        }
    }

    async syncDepth() {
        for (const [exchangeId, exchange] of this.exchangesConfig) {
            try {
                const markets = await this.prisma.markets.findMany({
                    where: {
                        exchange_id: exchangeId,
                    },
                    select: {
                        id: true,
                        base_token_id: true,
                        pair: true,
                    }
                })
                if (markets && markets.length > 0) {
                    const coinsPrice = await coinStatsModel.fetchCoinsPrice();
                    for (const market of markets) {
                        const coin = coinsPrice.find(item => item.coin_id === market.base_token_id);
                        const data = await exchange.fetchDepthData(market.pair, coin?.price ?? '0');
                        await pairStatsModel.insertDepth(exchangeId, market.id, data.plus2Depth, data.minus2Depth)
                    }
                }
            } catch (error) {
                console.error(`Failed to sync raw data for ${exchange.name}:`, error);
            }
        }
    }

    async updateMarkets(exchangeId, markets) {
        try {
            const coins = await this.prisma.coins.findMany({
                select: {
                    id: true, symbol: true,
                }
            });
            const coinMap = new Map(coins.map(coin => [coin.symbol, coin.id]));

            const validMarkets = markets.filter(market => {
                const baseExists = coinMap.has(market.base_token_id);
                const quoteExists = (market.base_token_id === 'USDT') ? (market.quote_token_id === 'USDC') :( market.quote_token_id === 'USDT');
                return baseExists && quoteExists;
            });
            const marketsToUpsert = validMarkets.map(market => ({
                where: {
                    exchange_id_pair: {
                        exchange_id: exchangeId, pair: market.pair
                    }
                }, create: {
                    exchange_id: exchangeId,
                    base_token_id: coinMap.get(market.base_token_id),
                    quote_token_id: coinMap.get(market.quote_token_id),
                    pair: market.pair,
                    is_active: true
                }, update: {
                    base_token_id: coinMap.get(market.base_token_id),
                    quote_token_id: coinMap.get(market.quote_token_id),
                    is_active: true
                }
            }));

            await this.prisma.$transaction(marketsToUpsert.map(market => this.prisma.markets.upsert(market)));

            console.log(`Successfully processed ${validMarkets.length} markets for exchange ${exchangeId}`);
        } catch (error) {
            console.error(`Error updating markets for exchange ${exchangeId}:`, error);
            throw error;
        }
    }

    async updateMarketData(exchangeId, marketData) {
        try {
            let totalSpotVolume = 0;
            for (const data of marketData) {
                const market = await this.prisma.markets.findFirst({
                    where: {
                        pair: data.symbol,
                    },
                    select: {
                        id: true,
                    }
                });

                if (market) {
                    const volPer = ((data.volume * data.lastPrice) / data.quoteVolume);
                    await pairStatsModel.insertVolumePercentage(exchangeId, market.id, volPer);
                    totalSpotVolume += data.quoteVolume;
                }
            }
            await exchangeStatsModel.insertSpotVolume(exchangeId, totalSpotVolume);
        } catch (error) {
            console.error(`[ERROR] Failed to update market data for exchange ${exchangeId}:`, error);
            throw error;
        }
    }

    async processRawData(rawData, marketData) {
        try {
            for (const data of rawData) {
                const market = marketData.find((i) => i.pair === data.symbol);
                const supply = await this.prisma.supply_data.findFirst({
                    where: {
                        token_id: market.base_token_id,
                    },
                    select: {
                        circulating_supply: true
                    }
                });
                if (market) {
                    const cirSupply = parseFloat(`${supply?.circulating_supply ?? 0}`)
                    priceHistoricalModel.insertPriceData(data.price, new Date(), data.volume, cirSupply, market.base_token_id).then();
                    pairStatsModel.insertVolume(market.exchange_id, market.id, data.volume).then()
                }
            }
        } catch (e) {
            console.log('Inserting failed', e)
        }
    }

    async calculateAvgLiquidity() {
        const ORDER_SIZES = [100, 500, 1000, 5000, 10000, 50000, 100000, 200000];

        for (const [exchangeId, exchange] of this.exchangesConfig) {
            try {
                const markets = await this.prisma.markets.findMany({
                    where: { exchange_id: exchangeId },
                    select: { pair: true }
                });

                if (!markets || markets.length === 0) {
                    continue;
                }

                const pairs = await Promise.all(
                    markets.map(async ({ pair }) => {
                        const score = await computePairScore(pair, exchange);
                        return { symbol: pair, score };
                    })
                );

                const avgLiquidity = pairs.reduce((sum, p) => sum + p.score, 0) / pairs.length;

                exchangeStatsModel.insertAvgLiquidity(exchangeId, avgLiquidity).then();
            } catch (error) {
                console.error(`❌ Failed to calculate avg liquidity for ${exchange.name}:`, error);
            }
        }

        async function computePairScore(pair, exchange) {
            const book = await exchange.fetchOrderBook(pair);
            if (book.bids.length <= 0 &&  book.asks.length <= 0) {
                return 0;
            }
            const bestBid = parseFloat(book.bids[0][0]);
            const bestAsk = parseFloat(book.asks[0][0]);
            const mid = (bestBid + bestAsk) / 2;

            const scores = [];

            for (const size of ORDER_SIZES) {
                const buySlip = simulateSlippage(book.asks, size, mid);
                const sellSlip = simulateSlippage(book.bids, size, mid);

                if (buySlip != null) {
                    const s = slippageScore(buySlip);
                    scores.push(s);
                }
                if (sellSlip != null) {
                    const s = slippageScore(sellSlip);
                    scores.push(s);
                }
            }

            if (scores.length === 0) {
                return 0;
            }

            return scores.reduce((a, b) => a + b, 0) / scores.length;
        }

        function simulateSlippage(sideOrders, sizeUSD, midPrice) {
            let remaining = sizeUSD;
            let cost = 0;
            for (const [priceStr, qtyStr] of sideOrders) {
                const price = parseFloat(priceStr), qty = parseFloat(qtyStr);
                const notional = price * qty;
                const used = Math.min(notional, remaining);
                cost += used * price;
                remaining -= used;
                if (remaining <= 0) break;
            }
            if (remaining > 0) return null;
            const avgPrice = cost / sizeUSD;
            return Math.abs(avgPrice - midPrice) / midPrice;
        }

        function slippageScore(slippage) {
            if (slippage >= 0.05) return 0;
            return Math.round((1 - slippage / 0.05) * 1000);
        }
    }
}

module.exports = ExchangeManager; 