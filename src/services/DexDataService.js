const axios = require('axios');
const {ethers} = require('ethers');

class DexDataService {
    async fetchDexPairs(url, limit = 1000) {
        const query = "query GetAllPools { pools(first: 1000, orderBy: totalValueLockedUSD, orderDirection: desc, subgraphError: allow) { id feeTier liquidity sqrtPrice tick token0 { id symbol name decimals derivedETH } token1 { id symbol name decimals derivedETH } token0Price token1Price volumeUSD volumeToken0 volumeToken1 txCount totalValueLockedToken0 totalValueLockedToken1 totalValueLockedUSD } bundles(where: { id: \"1\" }) { ethPriceUSD } }";

        try {
            const response = await axios.post(url, {
                query,
                variables: {limit}
            });

            return response.data.data.pools;
        } catch (error) {
            console.error(`Error fetching pairs for ${url}:`, error);
            return [];
        }
    }

    async fetchPairStats(pairAddress, url) {
        const query = `
              query GetPoolById($poolId: String!) {
                    pool(id: $poolId) {
                          id
                          token0 {
                                id
                                symbol
                                name
                                decimals
                                derivedETH
                          }
                          token1 {
                                id
                                symbol
                                name
                                decimals
                                derivedETH
                          }
                          volumeUSD
                          txCount
                          totalValueLockedToken0
                          totalValueLockedToken1
                          totalValueLockedUSD
                    }
                    bundles(where: { id: "1" }) {
                         ethPriceUSD
                    }
              }
            `;

        try {
            const response = await axios.post(url, {
                query,
                variables: {poolId: pairAddress.toLowerCase()}
            });

            return this.calculateDexPoolStats(response.data.data.pool, response.data.data.bundles[0].ethPriceUSD);
        } catch (error) {
            console.error(`Error fetching pair stats for ${pairAddress}:`, error);
            return null;
        }
    }

    // Fetch OHLCV data
    async fetchOHLCVData(pairAddress, protocolType, interval = '1h', days = 30) {
        const query = `
            query GetOHLCV($pairAddress: String!, $timestamp: Int!) {
                pairHourDatas(
                    where: { pair: $pairAddress, hourStartUnix_gte: $timestamp }
                    orderBy: hourStartUnix
                    orderDirection: asc
                ) {
                    hourStartUnix
                    open
                    high
                    low
                    close
                    volumeUSD
                }
            }
        `;

        const timestamp = Math.floor(Date.now() / 1000) - (days * 24 * 60 * 60);

        try {
            const response = await axios.post(this.graphqlEndpoints[protocolType], {
                query,
                variables: {pairAddress: pairAddress.toLowerCase(), timestamp}
            });

            return response.data.data.pairHourDatas;
        } catch (error) {
            console.error(`Error fetching OHLCV data for ${pairAddress}:`, error);
            return [];
        }
    }


    calculateDexPoolStats(poolData, ethUsdPrice) {
        const {
            token0,
            token1,
            totalValueLockedToken0,
            totalValueLockedToken1,
            volumeUSD,
            txCount,
            totalValueLockedUSD
        } = poolData;

        const toFloat = (val) => parseFloat(val || "0");

        const token0DerivedETH = toFloat(token0.derivedETH);
        const token1DerivedETH = toFloat(token1.derivedETH);
        const token0TVL = toFloat(totalValueLockedToken0);
        const token1TVL = toFloat(totalValueLockedToken1);

        const token0PriceUSD = token0DerivedETH * ethUsdPrice;
        const token1PriceUSD = token1DerivedETH * ethUsdPrice;

        const liquidityUSD = (token0DerivedETH * token0TVL + token1DerivedETH * token1TVL) * ethUsdPrice;

        return {
            priceUSD: token0PriceUSD,
            liquidityUSD: totalValueLockedUSD ? toFloat(totalValueLockedUSD) : liquidityUSD,
            volume24hUSD: toFloat(volumeUSD),
            txns: toFloat(txCount),
        };
    }

}

module.exports = new DexDataService();