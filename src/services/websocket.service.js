const WebSocket = require('ws');
const coinController = require("../controllers/coin.controller");

class WebSocketService {
    constructor(wss, prisma) {
        this.wss = wss;
        this.prisma = prisma;
        this.broadcastInterval = null;
        this.coinTypeBroadcastInterval = null;
        this.coinDeatilBroadcastInterval = null;
        this.clients = new Map();
        this.defaultPage = 1;
        this.defaultLimit = 20;
        this.defaultCoinTypeLimit = 10;
    }

    initialize() {
        this.setupWebSocketHandlers();
        this.startCoinDataBroadcast();
        this.startCoinTypeBroadcast();
        this.startCoinDetailBroadcast();
        console.log('WebSocket service initialized');
    }

    setupWebSocketHandlers() {
        this.wss.on('connection', (ws) => {
            const clientId = this.generateClientId();
            console.log(`Client connected: ${clientId}`);

            this.clients.set(ws, {
                id: clientId,
                subscriptions: new Set(),
                isAlive: true,
                page: this.defaultPage,
                limit: this.defaultLimit,
                coinTypeSubscriptions: new Map(),
                coinDetailSubscriptions: new Map(),
            });

            ws.on('message', (message) => {
                try {
                    const data = JSON.parse(message.toString());
                    this.handleMessage(ws, data);
                } catch (error) {
                    console.error('Error parsing message:', error);
                    this.sendError(ws, 'Invalid message format');
                }
            });

            ws.on('close', () => {
                const client = this.clients.get(ws);
                if (client) {
                    console.log(`Client disconnected: ${client.id}`);
                    this.clients.delete(ws);
                }
            });

            ws.on('error', (error) => {
                console.error('WebSocket error:', error);
            });

            ws.on('pong', () => {
                const client = this.clients.get(ws);
                if (client) {
                    client.isAlive = true;
                }
            });
        });

        // Set up heartbeat interval to detect dead connections
        this.setupHeartbeat();
    }

    handleMessage(ws, data) {
        const client = this.clients.get(ws);
        if (!client) return;

        switch (data.type) {
            case 'subscribe:coins':
                client.subscriptions.add('coins');
                client.page = data.page || this.defaultPage;
                client.limit = data.limit || this.defaultLimit;
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coins',
                    page: client.page,
                    limit: client.limit
                });
                this.sendCoins(client.page, client.limit, ws).then();
                break;

            case 'unsubscribe:coins':
                client.subscriptions.delete('coins');
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coins',
                    message: 'Unsubscribed from coin updates'
                });
                break;

            case 'subscribe:coinType':
                const coinType = data.coinType; // 0 = trending, 1 = new
                const coinTypeLimit = data.limit || this.defaultCoinTypeLimit;
                
                if (coinType !== 0 && coinType !== 1) {
                    this.sendError(ws, 'Invalid coin type. Use 0 for trending or 1 for new coins');
                    break;
                }

                client.coinTypeSubscriptions.set(coinType, { limit: coinTypeLimit });
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coinType',
                    coinType: coinType,
                    limit: coinTypeLimit,
                    message: `Subscribed to ${coinType === 0 ? 'trending' : 'new'} coins`
                });
                this.sendCoinTypeData(coinType, coinTypeLimit, ws).then();
                break;

            case 'unsubscribe:coinType':
                const unsubCoinType = data.coinType;
                client.coinTypeSubscriptions.delete(unsubCoinType);
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coinType',
                    message: `Unsubscribed from ${unsubCoinType === 0 ? 'trending' : 'new'} coins`
                });
                break;

            case 'subscribe:coinDetail':
                const coinId = data.coin_id;

                client.coinDetailSubscriptions.set('coinDetail', {coinId: coinId});
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coinDetail',
                    coinID: coinId,
                });
                this.sendCoinDetail(coinId, ws).then();
                break;

            case 'unsubscribe:coinDetail':
                client.coinDetailSubscriptions.delete('coinDetail');
                this.sendMessage(ws, {
                    type: 'subscription:success',
                    subscription: 'coinDetail',
                    message: `Unsubscribed for coin detail`
                });
                break;

            case 'ping':
                this.sendMessage(ws, { type: 'pong' });
                break;

            default:
                this.sendError(ws, 'Unknown message type');
        }
    }

    async sendCoins(page, limit, ws) {
        const coinData = await coinController.getCoinsForWS(this.prisma, page, limit);
        const message = {
            type: 'coin:update',
            ...coinData
        };
        this.sendMessage(ws, message)
    }

    async sendCoinTypeData(type, limit, ws) {
        const coinData = await coinController.getCoinsByType(this.prisma, type, limit);
        const message = {
            type: 'coin:type:update',
            coinType: type,
            ...coinData
        };
        this.sendMessage(ws, message)
    }

    async sendCoinDetail(coinId, ws) {
        const coinDetail = await coinController.coinDetails(this.prisma, coinId);
        const message = {
            type: 'coin:Detail:update',
            ...coinDetail
        };
        this.sendMessage(ws, message)
    }

    sendMessage(ws, data) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(data));
        }
    }

    sendError(ws, message) {
        this.sendMessage(ws, {
            type: 'error',
            message: message
        });
    }

    startCoinDataBroadcast(){
        this.broadcastInterval = setInterval(async () => {
            const subscribedClients = this.getSubscribedClients('coins');
            if (subscribedClients.length > 0) {
                try {
                    const clientGroups = new Map();
                    subscribedClients.forEach(ws => {
                        const client = this.clients.get(ws);
                        if (client) {
                            const key = `${client.page || this.defaultPage}-${client.limit || this.defaultLimit}`;
                            if (!clientGroups.has(key)) {
                                clientGroups.set(key, {
                                    page: client.page || this.defaultPage,
                                    limit: client.limit || this.defaultLimit,
                                    clients: []
                                });
                            }
                            clientGroups.get(key).clients.push(ws);
                        }
                    });

                    for (const [key, group] of clientGroups) {
                        group.clients.forEach(ws => {
                            this.sendCoins(group.page, group.limit, ws);
                        });
                    }
                } catch (error) {
                    console.error('Error broadcasting coin data:', error);
                }
            }
        }, 30000); // 30 seconds

        console.log('Coin data broadcast started (30-second intervals)');
    }

    startCoinTypeBroadcast() {
        this.coinTypeBroadcastInterval = setInterval(async () => {
            const subscribedClients = this.getClientsWithCoinTypeSubscriptions();
            if (subscribedClients.length > 0) {
                try {
                    const clientGroups = new Map();
                    subscribedClients.forEach(({ ws, coinType, limit }) => {
                        const key = `${coinType}-${limit}`;
                        if (!clientGroups.has(key)) {
                            clientGroups.set(key, {
                                coinType,
                                limit,
                                clients: []
                            });
                        }
                        clientGroups.get(key).clients.push(ws);
                    });

                    for (const [key, group] of clientGroups) {
                        group.clients.forEach(ws => {
                            this.sendCoinTypeData(group.coinType, group.limit, ws);
                        });
                    }
                } catch (error) {
                    console.error('Error broadcasting coin type data:', error);
                }
            }
        }, 30000); // 10 seconds

        console.log('Coin type data broadcast started (10-second intervals)');
    }

    startCoinDetailBroadcast() {
        this.coinDeatilBroadcastInterval = setInterval(async () => {
            const subscribedClients = this.getClientsWithCoinDetailSubscriptions();
            if (subscribedClients.length > 0) {
                try {
                    const clientGroups = new Map();
                    subscribedClients.forEach(({ ws, coinId }) => {
                        const key = coinId;
                        if (!clientGroups.has(key)) {
                            clientGroups.set(key, {
                                coinId,
                                clients: []
                            });
                        }
                        clientGroups.get(key).clients.push(ws);
                    });

                    for (const [key, group] of clientGroups) {
                        group.clients.forEach(ws => {
                            this.sendCoinDetail(group.coinId, ws);
                        });
                    }
                } catch (error) {
                    console.error('Error broadcasting coin type data:', error);
                }
            }
        }, 30000); // 10 seconds

        console.log('Coin type data broadcast started (10-second intervals)');
    }

    stopCoinDataBroadcast() {
        if (this.broadcastInterval) {
            clearInterval(this.broadcastInterval);
            this.broadcastInterval = null;
            console.log('Coin data broadcast stopped');
        }
    }

    stopCoinTypeBroadcast() {
        if (this.coinTypeBroadcastInterval) {
            clearInterval(this.coinTypeBroadcastInterval);
            this.coinTypeBroadcastInterval = null;
            console.log('Coin type data broadcast stopped');
        }
    }

    stopCoinDetailBroadcast() {
        if (this.coinDeatilBroadcastInterval) {
            clearInterval(this.coinDeatilBroadcastInterval);
            this.coinDeatilBroadcastInterval = null;
            console.log('Coin detail data broadcast stopped');
        }
    }

    getSubscribedClients(subscription) {
        const subscribedClients = [];
        this.clients.forEach((client, ws) => {
            if (client.subscriptions.has(subscription) && ws.readyState === WebSocket.OPEN) {
                subscribedClients.push(ws);
            }
        });
        return subscribedClients;
    }

    getClientsWithCoinTypeSubscriptions() {
        const subscribedClients = [];
        this.clients.forEach((client, ws) => {
            if (ws.readyState === WebSocket.OPEN && client.coinTypeSubscriptions.size > 0) {
                client.coinTypeSubscriptions.forEach((config, coinType) => {
                    subscribedClients.push({
                        ws,
                        coinType,
                        limit: config.limit
                    });
                });
            }
        });
        return subscribedClients;
    }

    getClientsWithCoinDetailSubscriptions() {
        const subscribedClients = [];
        this.clients.forEach((client, ws) => {
            if (ws.readyState === WebSocket.OPEN && client.coinDetailSubscriptions.size > 0) {
                client.coinDetailSubscriptions.forEach((config) => {
                    subscribedClients.push({
                        ws,
                        coinId: config.coinId
                    });
                });
            }
        });
        return subscribedClients;
    }

    getConnectedClientsCount() {
        return this.clients.size;
    }

    broadcastMessage(messageType, data) {
        const message = {
            type: messageType,
            ...data
        };

        this.clients.forEach((client, ws) => {
            if (ws.readyState === WebSocket.OPEN) {
                this.sendMessage(ws, message);
            }
        });
    }

    generateClientId() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    setupHeartbeat() {
        setInterval(() => {
            this.clients.forEach((client, ws) => {
                if (!client.isAlive) {
                    console.log(`Terminating dead connection: ${client.id}`);
                    ws.terminate();
                    this.clients.delete(ws);
                    return;
                }

                client.isAlive = false;
                if (ws.readyState === WebSocket.OPEN) {
                    ws.ping();
                }
            });
        }, 30000); // Check every 30 seconds
    }

    destroy() {
        this.stopCoinDataBroadcast();
        this.stopCoinTypeBroadcast();
        this.stopCoinDetailBroadcast();
        
        // Close all connections
        this.clients.forEach((client, ws) => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.close();
            }
        });
        
        this.clients.clear();
        console.log('WebSocket service destroyed');
    }
}

module.exports = WebSocketService; 