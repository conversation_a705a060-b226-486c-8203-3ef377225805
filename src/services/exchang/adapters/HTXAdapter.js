const BaseExchangeAdapter = require("./BaseExchangeAdapter");

class HTXAdapter extends BaseExchangeAdapter {
    constructor(config = {}) {
        super(config);
        this.marketsEndpoint = config.marketsEndpoint;
        this.tickersEndpoint = config.tickersEndpoint;
        this.klineEndpoint = config.klineEndpoint;
        this.tradeEndpoint = config.tradeEndpoint;
        this.depthEndpoint = config.depthEndpoint;
    }

    async fetchMarkets() {
        const data = await this.makeRequest(this.marketsEndpoint);
        return this.standardizeMarket(data);
    }

    async fetchMarketData() {
        const data = await this.makeRequest(this.tickersEndpoint);
        return this.standardizeMarketData(data);
    }

    async fetchHistoricalData(symbol, startTime, endTime = Date.now()) {
        const limit = 2000;
        let start = (startTime instanceof Date) ? Math.floor(startTime.getTime() / 1000) : Math.floor(startTime / 1000);
        const end = (endTime instanceof Date) ? Math.floor(endTime.getTime() / 1000) : Math.floor(endTime / 1000);

        const finalData = [];

        while (start < end) {
            const params = {
                symbol,
                period: '15min',
                size: limit,
                from: start,
                to: end,
            };

            const res = await this.makeRequest(this.klineEndpoint, params);
            if (!res.length) break;

            finalData.push(...this.standardizeHistoricalData(res));
            const lastOpen = res[res.length - 1].id;
            start = lastOpen + 1;

            await new Promise(r => setTimeout(r, 200));
            if (res.length < limit) break;
        }
        return finalData;
    }

    async fetchRawData(symbols = []) {
        const data = await this.makeRequest(this.tradeEndpoint);
        const filterTicker = data.data.filter(ticker => symbols.includes(ticker.symbol));
        return this.standardizeRawData(filterTicker);
    }

    async fetchDepthData(symbol, price = null) {
        const params = {
            symbol: symbol,
            type: 'step0',
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return this.processDepth(data.tick.asks, data.tick.bids, price);
    }

    async fetchOrderBook(symbol) {
        const params = {
            symbol: symbol,
            type: 'step0',
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return data.tick;
    }

    standardizeMarket(rawData) {
        return rawData.data.map(symbol => ({
            exchange_id: this.config.id,
            base_token_id: symbol["base-currency"],
            quote_token_id: symbol["quote-currency"],
            pair: symbol.symbol,
            is_active: symbol.state === 'online',
        }));
    }

    standardizeMarketData(rawData) {
        return rawData.data.map(ticker => ({
            symbol: ticker.symbol,
            lastPrice: parseFloat(ticker.close),
            volume: parseFloat(ticker.vol),
            quoteVolume: parseFloat(ticker.close) * parseFloat(ticker.vol),
        }));
    }

    standardizeHistoricalData(rawData) {
        return rawData.map(kline => ({
            timestamp: new Date(kline.id * 1000),
            open: parseFloat(kline.open),
            close: parseFloat(kline.close),
            high: parseFloat(kline.high),
            low: parseFloat(kline.low),
            volume: parseFloat(kline.vol),
        }));
    }

    standardizeRawData(rawData) {
        return rawData.data.map(data => ({
            symbol: data.symbol,
            price: parseFloat(data.close),
            volume: parseFloat(data.vol),
        }));
    }

    async processDepth(asks, bids, price) {
        const plus2Depth = asks
            .filter(([askPrice]) => parseFloat(askPrice) <= price * 1.02)
            .map(([askPrice, qty]) => parseFloat(askPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        const minus2Depth = bids
            .filter(([bidPrice]) => parseFloat(bidPrice) >= price * 0.98)
            .map(([bidPrice, qty]) => parseFloat(bidPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        return { plus2Depth, minus2Depth };
    }
}

module.exports = HTXAdapter;
