const BaseExchangeAdapter = require('./BaseExchangeAdapter');

class BinanceAdapter extends BaseExchangeAdapter {
    constructor(config) {
        super(config);
        this.marketsEndpoint = config.marketsEndpoint;
        this.tickersEndpoint = config.tickersEndpoint;
        this.klineEndpoint = config.klineEndpoint;
        this.tradeEndpoint = config.tradeEndpoint;
        this.depthEndpoint = config.depthEndpoint;
    }

    async fetchMarkets() {
        const data = await this.makeRequest(this.marketsEndpoint);
        return this.standardizeMarket(data);
    }

    async fetchMarketData() {
        const data = await this.makeRequest(this.tickersEndpoint);
        return this.standardizeMarketData(data);
    }

    async fetchHistoricalData(symbol, startTime, endTime = Date.now()) {
        const limit = 300;
        let start = (startTime instanceof Date) ? Math.floor(startTime.getTime() / 1000) : Math.floor(startTime / 1000);

        const finalData = [];

        while (start < endTime) {
            const params = {
                instId: symbol,
                bar: '15m',
                after: start,
                before: endTime,
                limit: limit,
            };

            const res = await this.makeRequest(this.klineEndpoint, params);
            const list = res.data;
            if (!list.length) break;

            finalData.push(...this.standardizeHistoricalData(list));
            const lastOpen = list[list.length - 1][0];
            start = lastOpen + 1;

            await new Promise(r => setTimeout(r, 200));
            if (list.length < limit) break;
        }

        return finalData;
    }

    async fetchRawData(symbols) {
        const data = await this.makeRequest(this.tradeEndpoint);
        const filterTicker = data.data.filter(ticker => symbols.includes(ticker.instId));
        return this.standardizeRawData(filterTicker);
    }

    async fetchDepthData(symbol, price) {
        const params = {
            instId: symbol,
            sz: 300,
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return this.processDepth(data.data[0].asks, data.data[0].bids, price);
    }

    async fetchOrderBook(symbol) {
        const params = {
            instId: symbol,
            sz: 300,
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return data.data[0];
    }

    standardizeMarket(rawData) {
        return rawData.data.map(symbol => ({
            exchange_id: this.config.id,
            base_token_id: symbol.baseCcy,
            quote_token_id: symbol.quoteCcy,
            pair: symbol.instId,
            is_active: symbol.state === 'live',
        }));
    }

    standardizeMarketData(rawData) {
        return rawData.data.map(ticker => ({
            symbol: ticker.instId,
            lastPrice: parseFloat(ticker.last),
            volume: parseFloat(ticker.vol24h),
            quoteVolume: parseFloat(ticker.volCcy24h),
        }));
    }

    standardizeHistoricalData(rawData) {
        return rawData.data.map(kline => ({
            timestamp: new Date(kline[0]),
            open: parseFloat(kline[1]),
            close: parseFloat(kline[4]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            volume: parseFloat(kline[5]),
        }));
    }

    standardizeRawData(rawData) {
        return rawData.map(data => ({
            symbol: data.instId,
            price: parseFloat(data.last),
            volume: parseFloat(data.vol24h),
        }));
    }

    async processDepth(asks, bids, price) {
        const plus2Depth = asks
            .filter(([askPrice]) => parseFloat(askPrice) <= price * 1.02)
            .map(([askPrice, qty]) => parseFloat(askPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        const minus2Depth = bids
            .filter(([bidPrice]) => parseFloat(bidPrice) >= price * 0.98)
            .map(([bidPrice, qty]) => parseFloat(bidPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        return { plus2Depth, minus2Depth };
    }
}

module.exports = BinanceAdapter; 