const BaseExchangeAdapter = require('./BaseExchangeAdapter');

class KuCoinAdapter extends BaseExchangeAdapter {
    constructor(config) {
        super(config);
        this.marketsEndpoint = config.marketsEndpoint;
        this.tickersEndpoint = config.tickersEndpoint;
        this.klineEndpoint = config.klineEndpoint;
        this.tradeEndpoint = config.tradeEndpoint;
        this.depthEndpoint = config.depthEndpoint;
    }

    async fetchMarkets() {
        const data = await this.makeRequest(this.marketsEndpoint);
        return this.standardizeMarket(data);
    }

    async fetchMarketData() {
        const data = await this.makeRequest(this.tickersEndpoint);
        return this.standardizeMarketData(data);
    }

    async fetchHistoricalData(symbol, startTime, endTime = Date.now()) {
        const limit = 1500;
        let start = (startTime instanceof Date) ? Math.floor(startTime.getTime() / 1000) : Math.floor(startTime / 1000);;

        const finalData = [];

        while (start < endTime) {
            const params = {
                symbol: symbol,
                type: '5min',
                startAt: start,
                endAt: (endTime instanceof Date) ? Math.floor(endTime.getTime() / 1000) : Math.floor(endTime / 1000),
                limit: limit,
            };

            const res = await this.makeRequest(this.klineEndpoint, params);
            if (!res.length) break;

            finalData.push(...this.standardizeHistoricalData(res));
            const lastOpen = res[res.length - 1][0];
            start = lastOpen + 1;

            await new Promise(r => setTimeout(r, 200));
            if (res.length < limit) break;
        }

        return finalData;
    }

    async fetchRawData(symbols) {
        const data = await this.makeRequest(this.tradeEndpoint);
        const filterTicker = data.data.ticker.filter(ticker => symbols.includes(ticker.symbol));
        return this.standardizeRawData(filterTicker);
    }

    async fetchDepthData(symbol, price) {
        const params = {
            symbol: symbol,
            limit: 1000,
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return this.processDepth(data.data.asks, data.data.bids, price);
    }

    async fetchOrderBook(symbol) {
        const params = {
            symbol: symbol,
            limit: 1000,
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return data.data;
    }

    standardizeMarket(rawData) {
        return rawData.data.map(symbol => ({
            exchange_id: this.config.id,
            base_token_id: symbol.baseCurrency,
            quote_token_id: symbol.quoteCurrency,
            pair: symbol.symbol,
            is_active: `${symbol.enableTrading}` === 'true',
        }));
    }

    standardizeMarketData(rawData) {
        return rawData.data.ticker.map(ticker => ({
            symbol: ticker.symbol,
            lastPrice: parseFloat(ticker.last),
            volume: parseFloat(ticker.vol),
            quoteVolume: parseFloat(ticker.volValue)
        }));
    }

    standardizeHistoricalData(rawData) {
        return rawData.data.map(kline => ({
            timestamp: new Date(kline[0] * 1000),
            open: parseFloat(kline[1]),
            high: parseFloat(kline[3]),
            low: parseFloat(kline[4]),
            close: parseFloat(kline[2]),
            volume: parseFloat(kline[6]),
        }));
    }

    standardizeRawData(rawData) {
        return rawData.map(data => ({
            symbol: data.symbol,
            price: parseFloat(data.last),
            volume: parseFloat(data.vol),
        }));
    }

    async processDepth(asks, bids, price) {
        const plus2Depth = asks
            .filter(([askPrice]) => parseFloat(askPrice) <= price * 1.02)
            .map(([askPrice, qty]) => parseFloat(askPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        const minus2Depth = bids
            .filter(([bidPrice]) => parseFloat(bidPrice) >= price * 0.98)
            .map(([bidPrice, qty]) => parseFloat(bidPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        return { plus2Depth, minus2Depth };
    }
}

module.exports = KuCoinAdapter;