// BitgetAdapter.js
const BaseExchangeAdapter = require('./BaseExchangeAdapter');

class BitgetAdapter extends BaseExchangeAdapter {
    constructor(config) {
        super(config);
        this.marketsEndpoint = config.marketsEndpoint;
        this.tickersEndpoint = config.tickersEndpoint;
        this.klineEndpoint = config.klineEndpoint;
        this.tradeEndpoint = config.tradeEndpoint;
        this.depthEndpoint = config.depthEndpoint;
    }

    async fetchMarkets() {
        const data = await this.makeRequest(this.marketsEndpoint);
        // Bitget returns { code, msg, requestTime, data: [ ... ] }
        return this.standardizeMarket(data);
    }

    async fetchMarketData() {
        const data = await this.makeRequest(this.tickersEndpoint);
        // returns { code, msg, requestTime, data: [ ... ] }
        return this.standardizeMarketData(data);
    }

    /**
     * Fetch historical candlesticks for a symbol between startTime and endTime.
     * Bitget expects millisecond timestamps for startTime/endTime.
     * Pagination: uses limit (max 1000). We'll loop until endTime reached or API returns < limit.
     *
     * @param {string} symbol e.g. 'BTCUSDT'
     * @param {Date|number} startTime Date object or timestamp (seconds or ms)
     * @param {number} endTime timestamp in ms or Date; defaults to Date.now()
     */
    async fetchHistoricalData(symbol, startTime, endTime = Date.now()) {
        const limit = 1000;
        // normalize start to ms
        let start = this._normalizeToMs(startTime);
        let end = (endTime instanceof Date) ? endTime.getTime() : (typeof endTime === 'number' ? this._normalizeToMs(endTime) : Date.now());

        const finalData = [];

        while (start < end) {
            const params = {
                symbol: symbol,
                granularity: '15min', // bitget uses e.g. 1min, 3min, 5min, 15min, 30min, 1h etc.
                startTime: start,
                endTime: end,
                limit: limit,
            };

            const res = await this.makeRequest(this.klineEndpoint, params);
            // Bitget returns { code, msg, requestTime, data: [ [ts, open, high, low, close, vol, ...], ... ] }
            const raw = (res && res.data) ? res.data : res;

            if (!raw || !raw.length) break;

            finalData.push(...this.standardizeHistoricalData(raw));
            const lastOpen = parseInt(raw[raw.length - 1][0], 10);
            // move start forward to lastOpen + 1ms to avoid duplicates
            start = lastOpen + 1;

            // brief throttle
            await new Promise(r => setTimeout(r, 200));
            if (raw.length < limit) break;
        }

        return finalData;
    }

    /**
     * fetchRawData(symbols)
     * Bitget's spot fills endpoint returns recent trades for a single symbol.
     * To emulate BinanceAdapter behavior (accept multiple symbols), we'll fetch ticker list
     * and filter for requested symbols, otherwise fallback to fetching fills per symbol.
     */
    async fetchRawData(symbols = []) {
        // try to fetch tickers (batch); if not available / fails, fetch fills per symbol
        try {
            const tickersRes = await this.makeRequest(this.tickersEndpoint);
            const tickers = (tickersRes && tickersRes.data) ? tickersRes.data : tickersRes;

            // filter tickers if symbols provided
            let filtered = tickers;
            if (Array.isArray(symbols) && symbols.length) {
                const set = new Set(symbols.map(s => s.toUpperCase()));
                filtered = tickers.filter(t => set.has((t.symbol || '').toUpperCase()));
            }

            return this.standardizeRawData(filtered);
        } catch (err) {
            // fallback: fetch recent trades for each symbol
            const results = [];
            for (const s of symbols) {
                const params = { symbol: s, limit: 100 };
                const r = await this.makeRequest(this.tradeEndpoint, params);
                const raw = (r && r.data) ? r.data : r;
                // raw is array of trades for that symbol
                results.push(...this.standardizeRawData(raw));
                await new Promise(rp => setTimeout(rp, 150));
            }
            return results;
        }
    }

    async fetchDepthData(symbol, price) {
        const params = {
            symbol: symbol,
            type: 'step0', // default (step0 returns full granularity)
            limit: 150, // max documented 150 for V2 spot orderbook
        };

        const res = await this.makeRequest(this.depthEndpoint, params);
        const data = (res && res.data) ? res.data : res;
        // data: { asks: [[price, qty], ...], bids: [[price, qty], ...], ts }
        return this.processDepth(data.asks || [], data.bids || [], price);
    }

    async fetchOrderBook(symbol) {
        const params = {
            symbol: symbol,
            type: 'step0',
            limit: 150,
        };

        const res = await this.makeRequest(this.depthEndpoint, params);
        const data = (res && res.data) ? res.data : res;
        // data: { asks: [[price, qty], ...], bids: [[price, qty], ...], ts }
        return {
            asks: data.asks || [],
            bids: data.bids || [],
        }
    }

    /**********************
     * Standardizers
     **********************/

    standardizeMarket(rawData) {
        // rawData.data -> array of symbols
        const symbols = (rawData && rawData.data) ? rawData.data : rawData;
        return (symbols || []).map(s => ({
            exchange_id: this.config.id,
            base_token_id: s.baseCoin,
            quote_token_id: s.quoteCoin,
            pair: s.symbol,
            is_active: (s.status === 'online'),
        }));
    }

    standardizeMarketData(rawData) {
        // rawData.data -> array of tickers
        const tickers = (rawData && rawData.data) ? rawData.data : rawData;
        return (tickers || []).map(ticker => ({
            symbol: ticker.symbol,
            lastPrice: parseFloat(ticker.lastPr || ticker.lastPrice || 0),
            volume: parseFloat(ticker.baseVolume || ticker.volume || 0),
            quoteVolume: parseFloat(ticker.quoteVolume || ticker.usdtVolume || 0),
        }));
    }

    standardizeHistoricalData(rawData) {
        // rawData is array of arrays: [ [ts, open, high, low, close, vol, ...], ... ]
        return rawData.map(kline => ({
            timestamp: new Date(parseInt(kline[0], 10)),
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5]),
        }));
    }

    standardizeRawData(rawData) {
        // rawData could be tickers array or fills array
        // If objects have 'price' or 'lastPr' or 'price' strings, handle both forms.
        if (!rawData) return [];

        // Detect if items are ticker-like or trade-like
        const sample = rawData[0] || {};
        if (sample.hasOwnProperty('price') || sample.hasOwnProperty('size') || sample.hasOwnProperty('tradeId')) {
            // trade-like (fills)
            return rawData.map(d => ({
                symbol: d.symbol,
                price: parseFloat(d.price || d.lastPr || 0),
                volume: parseFloat(d.size || d.qty || d.volume || 0),
            }));
        } else if (sample.hasOwnProperty('lastPr') || sample.hasOwnProperty('lastPrice')) {
            // ticker-like
            return rawData.map(d => ({
                symbol: d.symbol,
                price: parseFloat(d.lastPr || d.lastPrice || 0),
                volume: parseFloat(d.baseVolume || d.volume || 0),
            }));
        } else {
            // unknown structure; attempt generic mapping
            return rawData.map(d => ({
                symbol: d.symbol || null,
                price: d.price ? parseFloat(d.price) : (d.lastPr ? parseFloat(d.lastPr) : null),
                volume: d.size ? parseFloat(d.size) : (d.volume ? parseFloat(d.volume) : null),
            }));
        }
    }

    async processDepth(asks = [], bids = [], price) {
        // asks: [ [priceStr, qtyStr], ... ]
        // bids: [ [priceStr, qtyStr], ... ]
        const p = parseFloat(price);

        const plus2Depth = (asks || [])
            .filter(([askPrice]) => parseFloat(askPrice) <= p * 1.02)
            .map(([askPrice, qty]) => parseFloat(askPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        const minus2Depth = (bids || [])
            .filter(([bidPrice]) => parseFloat(bidPrice) >= p * 0.98)
            .map(([bidPrice, qty]) => parseFloat(bidPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        return { plus2Depth, minus2Depth };
    }

    /**********************
     * Helpers
     **********************/
    _normalizeToMs(input) {
        if (input instanceof Date) return input.getTime();
        if (typeof input === 'number') {
            // if timestamp seems in seconds (less than 1e12), convert to ms
            return (input < 1e12) ? input * 1000 : input;
        }
        // fallback: try parse
        const parsed = parseInt(input, 10);
        if (isNaN(parsed)) return Date.now();
        return (parsed < 1e12) ? parsed * 1000 : parsed;
    }
}

module.exports = BitgetAdapter;
