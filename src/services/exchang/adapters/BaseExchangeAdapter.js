const axios = require('axios');
class BaseExchangeAdapter {
    constructor(config) {
        this.config = config;
        this.name = config.name;
        this.baseUrl = config.apiEndpoint;
        this.pairFormat = config.tradingPairFormat;
        this.apiResponseFormat = config.apiResponseFormat;
    }

    // Required methods that each exchange must implement
    async fetchMarkets() {
        throw new Error('Method not implemented');
    }

    async fetchMarketData() {
        throw new Error('Method not implemented');
    }

    async fetchHistoricalData(symbol, startTime, endTime) {
        throw new Error('Method not implemented');
    }

    async fetchRawData(symbols) {
        throw new Error('Method not implemented');
    }

    async fetchDepthData(symbol, price) {
        throw new Error('Method not implemented');
    }

    async fetchOrderBook(symbol) {
        throw new Error('Method not implemented');
    }

    standardizeMarket(rawData) {
        throw new Error('Method not implemented');
    }

    standardizeMarketData(rawData) {
        throw new Error('Method not implemented');
    }

    standardizeHistoricalData(rawData) {
        throw new Error('Method not implemented');
    }

    standardizeRawData(rawData) {
        throw new Error('Method not implemented');
    }

    async processDepth(asks, bids, price) {
        throw new Error('Method not implemented');
    }

    // Common utility methods
    async makeRequest(endpoint, params = {}) {
        try {
            const response = await axios.get(`${this.baseUrl}${endpoint}`, { params });
            return response.data;
        } catch (error) {
            console.error(`Error fetching data from ${this.name}:`, error);
            throw error;
        }
    }
}

module.exports = BaseExchangeAdapter; 