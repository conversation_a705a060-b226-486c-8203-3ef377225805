const BaseExchangeAdapter = require('./BaseExchangeAdapter');

class BinanceAdapter extends BaseExchangeAdapter {
    constructor(config) {
        super(config);
        this.marketsEndpoint = config.marketsEndpoint;
        this.tickersEndpoint = config.tickersEndpoint;
        this.klineEndpoint = config.klineEndpoint;
        this.tradeEndpoint = config.tradeEndpoint;
        this.depthEndpoint = config.depthEndpoint;
    }

    async fetchMarkets() {
        const data = await this.makeRequest(this.marketsEndpoint);
        return this.standardizeMarket(data);
    }

    async fetchMarketData() {
        const data = await this.makeRequest(this.tickersEndpoint);
        return this.standardizeMarketData(data);
    }

    async fetchHistoricalData(symbol, startTime, endTime = Date.now()) {
        const limit = 1000;
        let start = (startTime instanceof Date) ? Math.floor(startTime.getTime() / 1000) : Math.floor(startTime / 1000);

        const finalData = [];

        while (start < endTime) {
            const params = {
                symbol: symbol,
                interval: '15m',
                startTime: start,
                endTime: endTime,
                limit: limit,
            };

            const res = await this.makeRequest(this.klineEndpoint, params);
            if (!res.length) break;

            finalData.push(...this.standardizeHistoricalData(res));
            const lastOpen = res[res.length - 1][0];
            start = lastOpen + 1;

            await new Promise(r => setTimeout(r, 200));
            if (res.length < limit) break;
        }

        return finalData;
    }

    async fetchRawData(symbols) {
        const params = {
            symbols: JSON.stringify(symbols),
        };

        const data = await this.makeRequest(this.tradeEndpoint, params);
        return this.standardizeRawData(data);
    }

    async fetchDepthData(symbol, price) {
        const params = {
            symbol: symbol,
            limit: 1000,
        }

        const data = await this.makeRequest(this.depthEndpoint, params);
        return this.processDepth(data.asks, data.bids, price);
    }

    async fetchOrderBook(symbol) {
        const params = {
            symbol: symbol,
            limit: 1000,
        }

        return await this.makeRequest(this.depthEndpoint, params);
    }

    standardizeMarket(rawData) {
        return rawData.symbols.map(symbol => ({
            exchange_id: this.config.id,
            base_token_id: symbol.baseAsset,
            quote_token_id: symbol.quoteAsset,
            pair: symbol.symbol,
            is_active: symbol.status === 'TRADING',
        }));
    }

    standardizeMarketData(rawData) {
        return rawData.map(ticker => ({
            symbol: ticker.symbol,
            lastPrice: parseFloat(ticker.lastPrice),
            volume: parseFloat(ticker.volume),
            quoteVolume: parseFloat(ticker.quoteVolume),
        }));
    }

    standardizeHistoricalData(rawData) {
        return rawData.map(kline => ({
            timestamp: new Date(kline[0]),
            open: parseFloat(kline[1]),
            close: parseFloat(kline[4]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            volume: parseFloat(kline[5]),
        }));
    }

    standardizeRawData(rawData) {
        return rawData.map(data => ({
            symbol: data.symbol,
            price: parseFloat(data.lastPrice),
            volume: parseFloat(data.volume),
        }));
    }

    async processDepth(asks, bids, price) {
        const plus2Depth = asks
            .filter(([askPrice]) => parseFloat(askPrice) <= price * 1.02)
            .map(([askPrice, qty]) => parseFloat(askPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        const minus2Depth = bids
            .filter(([bidPrice]) => parseFloat(bidPrice) >= price * 0.98)
            .map(([bidPrice, qty]) => parseFloat(bidPrice) * parseFloat(qty))
            .reduce((sum, val) => sum + val, 0);

        return { plus2Depth, minus2Depth };
    }
}

module.exports = BinanceAdapter; 