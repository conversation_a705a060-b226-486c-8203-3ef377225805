const BinanceAdapter = require('./adapters/BinanceAdapter');
const KuCoinAdapter = require('./adapters/KucoinAdapter');

class ExchangeFactory {
    static createExchange(exchangeConfig) {
        switch (exchangeConfig.name) {
            case 'Binance':
                return new BinanceAdapter(exchangeConfig);
            case 'KuCoin':
                return new KuCoinAdapter(exchangeConfig);
            default:
                throw new Error(`Unsupported exchange type: ${exchangeConfig.type}`);
        }
    }
}

module.exports = ExchangeFactory; 