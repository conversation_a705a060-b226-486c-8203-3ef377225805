// const elasticsearchService = require('./ElasticsearchService');
//
// class SyncService {
//     constructor(prisma) {
//         this.prisma = prisma;
//     }
//
//     async syncCoins() {
//         try {
//             const coins = await this.prisma.coins.findMany({
//                 include: {
//                     coin_metadata: true,
//                     supply_data: true
//                 }
//             });
//
//             for (const coin of coins) {
//                 await elasticsearchService.indexCoin(coin);
//             }
//
//             console.log(`Successfully synced ${coins.length} coins to Elasticsearch`);
//         } catch (error) {
//             console.error('Error syncing coins to Elasticsearch:', error);
//             throw error;
//         }
//     }
//
//     async syncExchanges() {
//         try {
//             const exchanges = await this.prisma.exchanges.findMany({
//                 include: {
//                     exchange_metadata: true
//                 }
//             });
//
//             for (const exchange of exchanges) {
//                 await elasticsearchService.indexExchange(exchange);
//             }
//
//             console.log(`Successfully synced ${exchanges.length} exchanges to Elasticsearch`);
//         } catch (error) {
//             console.error('Error syncing exchanges to Elasticsearch:', error);
//             throw error;
//         }
//     }
//
//     async syncAll() {
//         await this.syncCoins();
//         await this.syncExchanges();
//     }
//
//     // Hook methods to be called after database operations
//     async afterCoinCreated(coin) {
//         await elasticsearchService.indexCoin(coin);
//     }
//
//     async afterCoinUpdated(coin) {
//         await elasticsearchService.indexCoin(coin);
//     }
//
//     async afterCoinDeleted(coinId) {
//         await elasticsearchService.deleteCoin(coinId);
//     }
//
//     async afterExchangeCreated(exchange) {
//         await elasticsearchService.indexExchange(exchange);
//     }
//
//     async afterExchangeUpdated(exchange) {
//         await elasticsearchService.indexExchange(exchange);
//     }
//
//     async afterExchangeDeleted(exchangeId) {
//         await elasticsearchService.deleteExchange(exchangeId);
//     }
// }
//
// module.exports = SyncService;