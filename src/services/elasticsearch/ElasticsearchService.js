// const { client, indices } = require('../../configs/elasticsearch.client');
//
// class ElasticsearchService {
//     constructor() {
//         this.client = client;
//         this.indices = indices;
//     }
//
//     async createIndices() {
//         try {
//             // Create coins index
//             const coinsExists = await this.client.indices.exists({ index: this.indices.coins });
//             if (!coinsExists) {
//                 await this.client.indices.create({
//                     index: this.indices.coins,
//                     body: {
//                         mappings: {
//                             properties: {
//                                 name: { type: 'text', fields: { keyword: { type: 'keyword' } } },
//                                 symbol: { type: 'keyword' },
//                                 slug: { type: 'keyword' },
//                                 description: { type: 'text' },
//                                 logo_url: { type: 'keyword' },
//                                 launch_date: { type: 'date' },
//                                 is_active: { type: 'boolean' },
//                                 created_at: { type: 'date' },
//                                 metadata: {
//                                     properties: {
//                                         website: { type: 'keyword' },
//                                         explorer_urls: { type: 'keyword' },
//                                         contracts: { type: 'keyword' },
//                                         social_urls: { type: 'keyword' }
//                                     }
//                                 },
//                                 supply_data: {
//                                     properties: {
//                                         circulating_supply: { type: 'double' },
//                                         total_supply: { type: 'double' },
//                                         max_supply: { type: 'double' },
//                                         max_supply_infinite: { type: 'boolean' },
//                                         last_updated: { type: 'date' }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 });
//             }
//
//             // Create exchanges index
//             const exchangesExists = await this.client.indices.exists({ index: this.indices.exchanges });
//             if (!exchangesExists) {
//                 await this.client.indices.create({
//                     index: this.indices.exchanges,
//                     body: {
//                         mappings: {
//                             properties: {
//                                 name: { type: 'text', fields: { keyword: { type: 'keyword' } } },
//                                 exchange_type: { type: 'keyword' },
//                                 logo_url: { type: 'keyword' },
//                                 slug: { type: 'keyword' },
//                                 created_at: { type: 'date' },
//                                 updated_at: { type: 'date' },
//                                 is_active: { type: 'boolean' },
//                                 launch_date: { type: 'date' },
//                                 about: { type: 'text' },
//                                 metadata: {
//                                     properties: {
//                                         landing_page_url: { type: 'keyword' },
//                                         login_url: { type: 'keyword' },
//                                         fees_url: { type: 'keyword' },
//                                         api_base_endpoint: { type: 'keyword' },
//                                         market_endpoint: { type: 'keyword' },
//                                         ticker_endpoint: { type: 'keyword' },
//                                         kline_endpoint: { type: 'keyword' },
//                                         twitter_url: { type: 'keyword' },
//                                         telegram_url: { type: 'keyword' },
//                                         pair_format: { type: 'keyword' },
//                                         api_response_format: { type: 'keyword' }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 });
//             }
//         } catch (error) {
//             console.error('Error creating Elasticsearch indices:', error);
//             throw error;
//         }
//     }
//
//     async indexCoin(coin) {
//         try {
//             const document = {
//                 name: coin.name,
//                 symbol: coin.symbol,
//                 slug: coin.slug,
//                 description: coin.description,
//                 logo_url: coin.logo_url,
//                 launch_date: coin.launch_date,
//                 is_active: coin.is_active,
//                 created_at: coin.created_at,
//                 metadata: coin.coin_metadata,
//                 supply_data: coin.supply_data
//             };
//
//             await this.client.index({
//                 index: this.indices.coins,
//                 id: coin.id,
//                 document
//             });
//         } catch (error) {
//             console.error('Error indexing coin:', error);
//             throw error;
//         }
//     }
//
//     async indexExchange(exchange) {
//         try {
//             const document = {
//                 name: exchange.name,
//                 exchange_type: exchange.exchange_type,
//                 logo_url: exchange.logo_url,
//                 slug: exchange.slug,
//                 created_at: exchange.createdAt,
//                 updated_at: exchange.updatedAt,
//                 is_active: exchange.is_active,
//                 launch_date: exchange.launch_date,
//                 about: exchange.about,
//                 metadata: exchange.exchange_metadata
//             };
//
//             await this.client.index({
//                 index: this.indices.exchanges,
//                 id: exchange.id,
//                 document
//             });
//         } catch (error) {
//             console.error('Error indexing exchange:', error);
//             throw error;
//         }
//     }
//
//     async searchCoins(query, options = {}) {
//         try {
//             const { from = 0, size = 20, filters = {} } = options;
//
//             const must = [
//                 {
//                     multi_match: {
//                         query,
//                         fields: ['name^2', 'symbol^2', 'description'],
//                         fuzziness: 'AUTO'
//                     }
//                 }
//             ];
//
//             if (filters.is_active !== undefined) {
//                 must.push({ term: { is_active: filters.is_active } });
//             }
//
//             const result = await this.client.search({
//                 index: this.indices.coins,
//                 body: {
//                     from,
//                     size,
//                     query: {
//                         bool: {
//                             must
//                         }
//                     },
//                     sort: [
//                         { _score: 'desc' },
//                         { created_at: 'desc' }
//                     ]
//                 }
//             });
//
//             return {
//                 total: result.hits.total.value,
//                 hits: result.hits.hits.map(hit => ({
//                     id: hit._id,
//                     score: hit._score,
//                     ...hit._source
//                 }))
//             };
//         } catch (error) {
//             console.error('Error searching coins:', error);
//             throw error;
//         }
//     }
//
//     async searchExchanges(query, options = {}) {
//         try {
//             const { from = 0, size = 20, filters = {} } = options;
//
//             const must = [
//                 {
//                     multi_match: {
//                         query,
//                         fields: ['name^2', 'about'],
//                         fuzziness: 'AUTO'
//                     }
//                 }
//             ];
//
//             if (filters.is_active !== undefined) {
//                 must.push({ term: { is_active: filters.is_active } });
//             }
//
//             if (filters.exchange_type) {
//                 must.push({ term: { exchange_type: filters.exchange_type } });
//             }
//
//             const result = await this.client.search({
//                 index: this.indices.exchanges,
//                 body: {
//                     from,
//                     size,
//                     query: {
//                         bool: {
//                             must
//                         }
//                     },
//                     sort: [
//                         { _score: 'desc' },
//                         { created_at: 'desc' }
//                     ]
//                 }
//             });
//
//             return {
//                 total: result.hits.total.value,
//                 hits: result.hits.hits.map(hit => ({
//                     id: hit._id,
//                     score: hit._score,
//                     ...hit._source
//                 }))
//             };
//         } catch (error) {
//             console.error('Error searching exchanges:', error);
//             throw error;
//         }
//     }
//
//     async deleteCoin(coinId) {
//         try {
//             await this.client.delete({
//                 index: this.indices.coins,
//                 id: coinId
//             });
//         } catch (error) {
//             console.error('Error deleting coin:', error);
//             throw error;
//         }
//     }
//
//     async deleteExchange(exchangeId) {
//         try {
//             await this.client.delete({
//                 index: this.indices.exchanges,
//                 id: exchangeId
//             });
//         } catch (error) {
//             console.error('Error deleting exchange:', error);
//             throw error;
//         }
//     }
// }
//
// module.exports = ElasticsearchService;