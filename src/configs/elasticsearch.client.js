// const { Client } = require('@elastic/elasticsearch');
//
// const elasticConfig = {
//     node: process.env.ELASTICSEARCH_NODE || 'http://localhost:9200',
//     auth: {
//         username: process.env.ELASTICSEARCH_USERNAME || 'elastic',
//         password: process.env.ELASTICSEARCH_PASSWORD || ''
//     },
//     maxRetries: 5,
//     requestTimeout: 60000,
//     sniffOnStart: true
// };
//
// const client = new Client(elasticConfig);
//
// module.exports = {
//     client,
//     indices: {
//         coins: 'coins',
//         exchanges: 'exchanges'
//     }
// };