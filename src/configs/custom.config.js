const isDebug = true;

// let prismaClient = undefined;

const dbHostMongo = isDebug ? '127.0.0.1' : '';
const dbPortMongo = '27017';
const dbNameMongo = isDebug ? 'coin_zu_zu' : '';
// const dbAuthMongo = isDebug ? '' : '';
// const dbUserMongo = isDebug ? '' : '';
// const dbPassMongo = isDebug ? '' : '';
const dbFinalQueryMongo = isDebug ? `mongodb://${dbHostMongo}:${dbPortMongo}` : '';

module.exports = {
    isDebug: isDebug,
    // getPrismaClient: () => prismaClient,
    // setPrismaClient: (value) => { prismaClient = value; },
    server: {
        port: 8044,
        corsOptions: {
            origin: "*"
        },
        requestTimeOut: 500000,
        allowedOrigin: "http://**************:5173",
        secretKey: "e4d4ffeda6b2cd0cd834800391840bc23124bb9c2cd558799f861573e8c9351a",
    },
    db: {
        mongo: {
            url: dbFinalQueryMongo,
            dbName: dbNameMongo,
        }
    },
    secretData: {
        jwtKey: '',
        issJwt: ''
    },
    // mail: {
    //     template: {
    //         views: `${__dirname}/../templates/mails/views/`,
    //         partials: `${__dirname}/../templates/mails/partials/`,
    //     },
    //     host: 'smtp.hostinger.com',
    //     port: 465,
    //     secure: true,
    //     username: '<EMAIL>',
    //     password: 'VeAS2q@MDtbA3zvmxLr62#VH&y5N^n8RbbAM$',
    //     from: '<EMAIL>',
    // },
    googleCaptcha: {
        siteKey: "",
        secreteKey: "",
    },
    image: {
        baseUrl: isDebug ? "http://**************:8044/data/" : "",
        chainIconUrl: "icons/blackchains/",
        coinIconUrl: "icons/coins/",
        dexIconUrl: "icons/dexs/",
        exchangeIconUrl: "icons/exchanges/",
        tokenIconUrl: "icons/tokens/",
        coinLogo: {
            resolutionWidth: 64,
            resolutionHeight: 64,
            size: 2,
        },
        exchangeLogo: {
            resolutionWidth: 64,
            resolutionHeight: 64,
            size: 2,
        },
    },
    apiData: {
        tradeCoinMaxItem: 5,
    },
    sponsor: {
        project_type: {
            COIN: 1,
            EXCHANGE: 2
        },
        sponsored_type: {
            DASHBOARD_TOP_SECTION: 1
        }
    }
}
