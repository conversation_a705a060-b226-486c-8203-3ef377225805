// const elasticsearchService = require('../services/elasticsearch/ElasticsearchService');
// const SyncService = require("../services/elasticsearch/SyncService");
//
// const initializeElasticSearch = async (prisma) => {
//     try {
//         const esService = new elasticsearchService();
//         await esService.createIndices();
//         console.log('Elasticsearch indices created successfully');
//
//         const syncService = new SyncService(prisma);
//         await syncService.syncAll();
//         console.log('Initial Elasticsearch sync completed successfully');
//
//     } catch (error) {
//         console.error('Error initializing services:', error);
//         process.exit(1);
//     }
// }
//
// module.exports = {
//     initializeElasticSearch
// };