const priceHistoricalModel = require("../models/priceHistorical.model");

exports.startKLineBuildJob = async (interval, intervalMinute, prisma) => {
    try {
        const marketBaseCoinIDs = await prisma.markets.findMany({
            where: {
                is_active: true,
            },
            select: {
                base_token_id: true,
            }
        });
        if (marketBaseCoinIDs && marketBaseCoinIDs.length > 0) {
            for (const market of marketBaseCoinIDs) {
                await priceHistoricalModel.buildKLineData(market.base_token_id, interval, intervalMinute);
            }
        }
    } catch (e) {
        console.error(`Error in KLine Builder job == ${e}`);
    }
}