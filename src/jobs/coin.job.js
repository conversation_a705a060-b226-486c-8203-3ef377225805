const axios = require("axios");
const coinStatsModel = require("../models/coinStats.model");

exports.getCoinData = async (prisma) => {
    try {
        const coins = await prisma.coins.findMany({
            select: {
                id: true, slug: true
            }, where: {
                is_active: true,
            }
        });

        const slugList = coins.map((coin) => (coin.slug));
        axios.get(`https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&ids=${slugList.join(',')}`).then(async (response) => {
            if (response.status === 200) {
                for (const coin of coins) {
                    const item = response.data.find(obj => obj.id === coin.slug);
                    if (item) {
                        await prisma.supply_data.upsert({
                            where: {
                                token_id: coin.id
                            }, create: {
                                token_id: coin.id,
                                circulating_supply: item.circulating_supply ? item.circulating_supply.toString() : null,
                                total_supply: item.total_supply ? item.total_supply.toString() : null,
                                max_supply: item.max_supply ? item.max_supply.toString() : null,
                                max_supply_infinite: item.max_supply === null,
                                last_updated: new Date()
                            }, update: {
                                circulating_supply: item.circulating_supply ? item.circulating_supply.toString() : null,
                                total_supply: item.total_supply ? item.total_supply.toString() : null,
                                max_supply: item.max_supply ? item.max_supply.toString() : null,
                                max_supply_infinite: item.max_supply === null,
                                last_updated: new Date()
                            }
                        });
                    }
                }
            }
        }).catch((error) => {
            console.error(`Get Coin Data Cron job Api error: ${error}`)
        });
    } catch (e) {
        console.error(`Get Coin Data Cron job error: ${e}`);
    }
}

exports.generateTrendingCoin = async (prisma) => {

    try {
        const map = {};

        const searches = await prisma.coin_searches.groupBy({
            by: ['coin_id'],
            _count: {
                coin_id: true,
            },
            where: {
                created_at: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
                },
            },
        });

        console.log()

        const views = await prisma.coin_page_view.groupBy({
            by: ['coin_id'],
            _count: {
                coin_id: true,
            },
            where: {
                created_at: {
                    gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // past 24 hours
                },
            },
        });

        const changes = await coinStatsModel.fetchLatestPriceChangeAllCoin();

        searches.forEach(({coin_id, count}) => {
            if (!map[coin_id]) map[coin_id] = {};
            map[coin_id].search = count;
        });

        views.forEach(({coin_id, count}) => {
            if (!map[coin_id]) map[coin_id] = {};
            map[coin_id].views = count;
        });

        changes.forEach(({_id, change}) => {
            if (!map[_id]) map[_id] = {};
            map[_id].change = change;
        });

        const filtered = Object.entries(map)
            .filter(([_, val]) => val.search || val.views || val.change)
            .map(([coin_id, val]) => ({coin_id, ...val}));

        const maxSearch = Math.max(...filtered.map(v => v.search || 0));
        const maxView = Math.max(...filtered.map(v => v.views || 0));
        const maxChange = Math.max(...filtered.map(v => v.change || 0));

        const result = filtered.map(item => {
            const s = (item.search || 0) / (maxSearch || 1);
            const v = (item.views || 0) / (maxView || 1);
            const c = (item.change || 0) / (maxChange || 1);
            const score = s * 0.4 + v * 0.4 + c * 0.2;
            return {...item, score};
        });

        const top10 = result.sort((a, b) => b.score - a.score);

        await Promise.all(
            top10.map((item, index) =>
                prisma.trending_coins.upsert({
                    where: {coin_id: item.coin_id},
                    update: {
                        score: item.score,
                        rank: index + 1,
                    },
                    create: {
                        coin_id: item.coin_id,
                        score: item.score,
                        rank: index + 1,
                    },
                })
            )
        );
    } catch (e) {
        console.error(`Error in Generating Trending Coin ===> ${e}`);
    }
}

exports.rankCoinsByMarketCap = async (prisma) => {
    const latestStats = await coinStatsModel.fetchLatestMarketCapPerCoin();

    try {
        await Promise.all(
            latestStats.map((item, index) =>
                prisma.market_cap_ranking.upsert({
                    where: {coin_id: item._id},
                    update: {
                        market_cap: item.market_cap,
                        rank: index + 1,
                    },
                    create: {
                        coin_id: item._id,
                        market_cap: item.market_cap,
                        rank: index + 1,
                    },
                })
            )
        );
    } catch (e) {
        console.error(`Error in Ranking Coins by Market Cap ===> ${e}`);
    }
}