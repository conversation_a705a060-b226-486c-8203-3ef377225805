const exchangeStatsModel = require("../models/exchangeStats.model")

exports.rankCoinsByTradingVolume = async (prisma) => {
    const latestStats = await exchangeStatsModel.getAllStats();

    await Promise.all(
        latestStats.map((item, index) =>
            prisma.exchange_ranking.upsert({
                where: { exchange_id: item.exchange_id },
                update: {
                    trading_volume: item.spot_volume,
                    rank: index + 1,

                },
                create: {
                    exchange_id: item.exchange_id,
                    trading_volume: item.spot_volume,
                    rank: index + 1,
                },
            })
        )
    );
}