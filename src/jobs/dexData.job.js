const dexDataService = require('../services/DexDataService');
const dexStatsModel = require('../models/dexStats.model');

exports.syncDexPairs = async (prisma) => {
    try {
        const protocols = await prisma.dex_protocols.findMany({
            where: { is_active: true }
        });

        for (const protocol of protocols) {
            const pairs = await dexDataService.fetchDexPairs(protocol.graphql_url);
            for (const pair of pairs) {
                const token0 = await prisma.tokens.findFirst({
                    where: {
                        blockchain_id: protocol.blockchain_id,
                        contract_address: pair.token0.id
                    },
                    select: { id: true }
                });

                if (!token0) {
                    continue;
                }

                const token1 = await prisma.tokens.findFirst({
                    where: {
                        blockchain_id: protocol.blockchain_id,
                        contract_address: pair.token1.id
                    },
                    select: { id: true }
                });

                if (!token1) {
                   continue;
                }

                await prisma.dex_pair.upsert({
                    where: {
                        protocol_id_blockchain_id_pair_address: {
                            protocol_id: protocol.id,
                            blockchain_id: protocol.blockchain_id,
                            pair_address: pair.id
                        }
                    },
                    create: {
                        protocol_id: protocol.id,
                        blockchain_id: protocol.blockchain_id,
                        pair_address: pair.id,
                        token0_id: token0.id,
                        token1_id: token1.id,
                        pair_symbol: `${pair.token0.symbol}/${pair.token1.symbol}`,
                        is_active: true
                    },
                    update: {
                        token0_id: token0.id,
                        token1_id: token1.id,
                        pair_symbol: `${pair.token0.symbol}/${pair.token1.symbol}`
                    }
                });
            }
        }
    } catch (error) {
        console.error('❌ Error syncing DEX pairs:', error);
    }
};

exports.updateDexPairStats = async (prisma) => {
    try {
        const pairs = await prisma.dex_pair.findMany({
            where: { is_active: true },
            include: {
                dex_protocols: true
            }
        });

        for (const pair of pairs) {
            const stats = await dexDataService.fetchPairStats(
                pair.pair_address, 
                pair.dex_protocols.graphql_url
            );

            if (stats) {
                await dexStatsModel.insertDexPairStats({
                    price_usd: parseFloat(stats.priceUSD || 0),
                    volume_24h_usd: parseFloat(stats.volume24hUSD || 0),
                    liquidity_usd: parseFloat(stats.liquidityUSD || 0),
                    txns: parseInt(stats.txns || 0),
                    timestamp: new Date()
                }, pair.id);
            }
        }
    } catch (error) {
        console.error('❌ Error updating DEX pair stats:', error);
    }
};

// Sync OHLCV data
exports.syncDexOHLCV = async (prisma) => {
    try {
        const pairs = await prisma.dex_pairs.findMany({
            where: { is_active: true },
            include: { dex_protocols: true }
        });

        for (const pair of pairs) {
            const ohlcvData = await dexDataService.fetchOHLCVData(
                pair.pair_address,
                pair.dex_protocols.protocol_type.toLowerCase()
            );

            for (const candle of ohlcvData) {
                await prisma.dex_ohlcv_data.upsert({
                    where: {
                        pair_id_interval_timestamp: {
                            pair_id: pair.id,
                            interval: 'H1',
                            timestamp: new Date(candle.hourStartUnix * 1000)
                        }
                    },
                    create: {
                        pair_id: pair.id,
                        interval: 'H1',
                        timestamp: new Date(candle.hourStartUnix * 1000),
                        open: parseFloat(candle.open || 0),
                        high: parseFloat(candle.high || 0),
                        low: parseFloat(candle.low || 0),
                        close: parseFloat(candle.close || 0),
                        volume_usd: parseFloat(candle.volumeUSD || 0)
                    },
                    update: {
                        open: parseFloat(candle.open || 0),
                        high: parseFloat(candle.high || 0),
                        low: parseFloat(candle.low || 0),
                        close: parseFloat(candle.close || 0),
                        volume_usd: parseFloat(candle.volumeUSD || 0)
                    }
                });
            }
        }

        console.log('✅ DEX OHLCV data synced successfully');
    } catch (error) {
        console.error('❌ Error syncing DEX OHLCV data:', error);
    }
};