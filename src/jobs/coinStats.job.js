const coinStatsModel = require("../models/coinStats.model");
const priceHistoricalModel = require("../models/priceHistorical.model");

const intervals = [
    { label: "1h", day: 1/24 },
    { label: "24h", day: 1 },
    { label: "7d", day: 7 },
];

exports.updateCoinStats = async (prisma) => {
    try {
        const coins = await prisma.coins.findMany({
            select: {
                id: true,
                supply_data: {
                    select: {
                        circulating_supply: true,
                    },
                },
            },
        });

        if (coins && coins.length > 0) {
            for (const coin of coins) {
                const latestDoc = await priceHistoricalModel.getKLineDataTrade(coin.id, '1H', 0);

                if (!latestDoc.length) continue;
                const latestPrice = latestDoc[latestDoc.length - 1].close;

                const changePercent = {};
                const volumes = {};

                for (const { label, day } of intervals) {
                    let pastDoc;
                    if (label === "1h") {
                        pastDoc = await priceHistoricalModel.getKLineDataTrade(coin.id, '5M', day);
                    } else  {
                        pastDoc = await priceHistoricalModel.getKLineDataTrade(coin.id, '1H', day);
                    }

                    volumes[label] = 0;

                    if (pastDoc && pastDoc.length > 0) {
                        const pastPrice = pastDoc[0].close;
                        changePercent[label] = ((latestPrice - pastPrice) / pastPrice) * 100;

                        pastDoc.forEach(doc => {
                            volumes[label] += Number(doc.volume || 0);
                        });
                    } else {
                        changePercent[label] = null;
                    }
                }

                const marketCap = parseFloat(`${latestPrice}`) * parseFloat(`${coin.supply_data.circulating_supply}`);

                const snapshot = {
                    timestamp: new Date(),
                    price: latestPrice,
                    change_percent: changePercent,
                    volume: volumes,
                    market_cap: marketCap,
                };

                await coinStatsModel.insertCoinStatsData(snapshot, coin.id);
            }
        }
    } catch (e) {
        console.error(`Error in Updating Coin Stats ===> ${e}`);
    }
}
