const express = require('express');
const nodemailer = require('nodemailer');
const hbs = require('nodemailer-express-handlebars');
const customConfig = require('../config/custom.config');
const i18n = require("../config/i18n.config");

const viewPath = customConfig.mail.template.views;
const partialsPath = customConfig.mail.template.partials;

const transporter = nodemailer.createTransport({
    host: customConfig.mail.host,
    port: customConfig.mail.port,
    secure: customConfig.mail.secure,
    auth: {
        user: customConfig.mail.username,
        pass: customConfig.mail.password,
    },
});

transporter.use('compile', hbs({
    viewEngine: {
        extName: '.handlebars',
        layoutsDir: viewPath,
        defaultLayout: false,
        partialsDir: partialsPath,
        express
    },
    viewPath: viewPath,
    extName: '.handlebars',
}))

exports.sendEmailVerifyMail = async (email, OTP) => {
    const mailOptions = {
        from: `MyCoinVote <${customConfig.mail.from}>`,
        to: email,
        subject: i18n.__('email_subject_login_otp'),
        template: 'send_otp',
        context: {
            OTP: OTP
        }
    };

    try {
        await transporter.sendMail(mailOptions);
        return true;
    } catch (error) {
        console.error('Error on sendEmailVerifyMail ======>');
        if(error) console.error(error);
        return false;
    }
}
