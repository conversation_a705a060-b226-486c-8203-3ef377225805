{"image_dimensions_must_be_300x300_pixels": "image_dimensions_must_be_300x300_pixels", "name_min_2_error": "name_min_2_error", "name_max_30_error": "name_max_30_error", "name_is_required": "name_is_required", "name_not_valid_string": "name_not_valid_string", "name_is_blank": "name_is_blank", "type_is_required": "type_is_required", "type_is_invalid": "type_is_invalid", "type_is_blank": "type_is_blank", "logo_is_required": "logo_is_required", "logo_not_valid": "logo_not_valid", "logo_is_blank": "logo_is_blank", "websiteUrl_link_is_required": "websiteUrl_link_is_required", "websiteUrl_link_not_valid": "websiteUrl_link_not_valid", "websiteUrl_link_is_blank": "websiteUrl_link_is_blank", "isLaunched_is_required": "isLaunched_is_required", "isLaunched_is_blank": "isLaunched_is_blank", "isLaunched_not_valid": "isLaunched_not_valid", "launch_date_is_required": "launch_date_is_required", "launch_date_is_blank": "launch_date_is_blank", "launch_date_too_old": "launch_date_too_old", "launch_date_too_future": "launch_date_too_future", "launch_date_format_error": "launch_date_format_error", "legalEntityName_is_required": "legalEntityName_is_required", "legalEntityName_is_blank": "legalEntityName_is_blank", "apiEndpoint_link_is_required": "apiEndpoint_link_is_required", "apiEndpoint_link_not_valid": "apiEndpoint_link_not_valid", "apiEndpoint_link_is_blank": "apiEndpoint_link_is_blank", "apiDocsUrl_link_is_required": "apiDocsUrl_link_is_required", "apiDocsUrl_link_not_valid": "apiDocsUrl_link_not_valid", "apiDocsUrl_link_is_blank": "apiDocsUrl_link_is_blank", "supportsWebSocket_is_required": "supportsWebSocket_is_required", "supportsWebSocket_is_blank": "supportsWebSocket_is_blank", "supportsWebSocket_not_valid": "supportsWebSocket_not_valid", "supportsRestApi_is_required": "supportsRestApi_is_required", "supportsRestApi_is_blank": "supportsRestApi_is_blank", "supportsRestApi_not_valid": "supportsRestApi_not_valid", "network_is_required": "network_is_required", "network_is_blank": "network_is_blank", "min_1_network_select": "min_1_network_select", "address_is_required": "address_is_required", "address_not_valid": "address_not_valid", "address_is_blank": "address_is_blank", "explorerUrl_link_is_required": "explorerUrl_link_is_required", "explorerUrl_link_not_valid": "explorerUrl_link_not_valid", "explorerUrl_link_is_blank": "explorerUrl_link_is_blank", "tradingPairFormat_is_required": "tradingPairFormat_is_required", "tradingPairFormat_is_blank": "tradingPairFormat_is_blank", "liquidityInfo_is_required": "liquidityInfo_is_required", "liquidityInfo_is_blank": "liquidityInfo_is_blank", "fiat_is_required": "fiat_is_required", "fiat_is_blank": "fiat_is_blank", "min_1_fiat_select": "min_1_fiat_select", "feeStructure_is_required": "feeStructure_is_required", "feeStructure_is_blank": "feeStructure_is_blank", "requiresKycAml_is_required": "requiresKycAml_is_required", "requiresKycAml_is_blank": "requiresKycAml_is_blank", "requiresKycAml_not_valid": "requiresKycAml_not_valid", "offers_section_is_required": "offers_section_is_required", "offers_section_is_blank": "offers_section_is_blank", "offers_section_contains_invalid_values": "offers_section_contains_invalid_values", "offers_section_must_be_string": "offers_section_must_be_string", "twitterHandle_is_required": "twitterHandle_is_required", "twitterHandle_is_blank": "twitterHandle_is_blank", "telegramUrl_link_is_required": "telegramUrl_link_is_required", "telegramUrl_link_not_valid": "telegramUrl_link_not_valid", "telegramUrl_link_is_blank": "telegramUrl_link_is_blank", "discordUrl_link_is_required": "discordUrl_link_is_required", "discordUrl_link_not_valid": "discordUrl_link_not_valid", "discordUrl_link_is_blank": "discordUrl_link_is_blank", "redditUrl_link_is_required": "redditUrl_link_is_required", "redditUrl_link_not_valid": "redditUrl_link_not_valid", "redditUrl_link_is_blank": "redditUrl_link_is_blank", "blogUrl_link_is_required": "blogUrl_link_is_required", "blogUrl_link_not_valid": "blogUrl_link_not_valid", "blogUrl_link_is_blank": "blogUrl_link_is_blank", "supportCenterUrl_link_is_required": "supportCenterUrl_link_is_required", "supportCenterUrl_link_not_valid": "supportCenterUrl_link_not_valid", "supportCenterUrl_link_is_blank": "supportCenterUrl_link_is_blank", "licenseInfo_is_required": "licenseInfo_is_required", "licenseInfo_is_blank": "licenseInfo_is_blank", "securityAuditUrl_link_is_required": "securityAuditUrl_link_is_required", "securityAuditUrl_link_not_valid": "securityAuditUrl_link_not_valid", "securityAuditUrl_link_is_blank": "securityAuditUrl_link_is_blank", "bugBountyUrl_link_is_required": "bugBountyUrl_link_is_required", "bugBountyUrl_link_not_valid": "bugBountyUrl_link_not_valid", "bugBountyUrl_link_is_blank": "bugBountyUrl_link_is_blank", "incidentHistory_is_required": "incidentHistory_is_required", "incidentHistory_is_blank": "incidentHistory_is_blank", "kycVendor_is_required": "kycVendor_is_required", "kycVendor_is_blank": "kycVendor_is_blank", "api_response_format_is_required": "api_response_format_is_required", "api_response_format_is_invalid": "api_response_format_is_invalid", "api_response_format_is_blank": "api_response_format_is_blank", "pairsEndpoint_link_is_required": "pairsEndpoint_link_is_required", "pairsEndpoint_link_not_valid": "pairsEndpoint_link_not_valid", "pairsEndpoint_link_is_blank": "pairsEndpoint_link_is_blank", "volumeEndpoint_link_is_required": "volumeEndpoint_link_is_required", "volumeEndpoint_link_not_valid": "volumeEndpoint_link_not_valid", "volumeEndpoint_link_is_blank": "volumeEndpoint_link_is_blank", "integrationNotes_is_required": "integrationNotes_is_required", "integrationNotes_is_blank": "integrationNotes_is_blank", "icon_not_exist": "icon_not_exist", "icon_upload_successfully": "icon_upload_successfully", "network_must_be_string": "network_must_be_string", "network_not_supported": "network_not_supported", "networks_must_be_array": "networks_must_be_array", "networks_must_be_unique": "networks_must_be_unique", "volume24h_is_required": "volume24h_is_required", "volume24h_is_blank": "volume24h_is_blank", "tradingPairsCount_is_required": "tradingPairsCount_is_required", "tradingPairsCount_is_blank": "tradingPairsCount_is_blank", "exchange_listed": "exchange_listed", "failed_to_fetch_coin_list": "Failed to fetch coin list", "coin_list": "Coin list retrieved successfully", "exchange_list_fetched": "Exchange list fetched successfully", "failed_to_fetch_exchanges": "Failed to fetch exchange list", "coin_not_found": "Coin not found", "coin_details": "Coin details retrieved successfully", "failed_to_fetch_coin_details": "Failed to fetch coin details", "chart_data": "Chart data retrieved successfully", "market_id_not_found": "Market not found for the specified coin", "failed_to_fetch_chart_data": "Failed to fetch chart data", "invalid_time_period": "Invalid time period. Supported periods are: 1d, 2d, 1m, 6m, 1y", "no_data_for_period": "No data available for the specified time period", "high_low_prices_fetched": "High and low prices fetched successfully", "failed_to_fetch_high_low_prices": "Failed to fetch high and low prices", "file_not_uploaded": "file_not_uploaded", "coin_name_min_1_error": "coin_name_min_1_error", "coin_name_max_100_error": "coin_name_max_100_error", "coin_name_is_required": "coin_name_is_required", "coin_name_is_blank": "coin_name_is_blank", "symbol_min_1_error": "symbol_min_1_error", "symbol_max_10_error": "symbol_max_10_error", "symbol_is_required": "symbol_is_required", "symbol_not_valid_format": "symbol_not_valid_format", "symbol_is_blank": "symbol_is_blank", "coin_logo_is_required": "coin_logo_is_required", "coin_logo_not_valid": "coin_logo_not_valid", "coin_logo_is_blank": "coin_logo_is_blank", "description_is_required": "description_is_required", "description_is_blank": "description_is_blank", "max_supply_infinite_is_required": "max_supply_infinite_is_required", "max_supply_infinite_is_blank": "max_supply_infinite_is_blank", "max_supply_infinite_not_valid": "max_supply_infinite_not_valid", "max_supply_is_required": "max_supply_is_required", "max_supply_is_blank": "max_supply_is_blank", "supply_data_url_link_is_required": "supply_data_url_link_is_required", "supply_data_url_link_not_valid": "supply_data_url_link_not_valid", "supply_data_url_link_is_blank": "supply_data_url_link_is_blank", "website_link_is_required": "website_link_is_required", "website_link_not_valid": "website_link_not_valid", "website_link_is_blank": "website_link_is_blank", "whitepaper_link_is_required": "whitepaper_link_is_required", "whitepaper_link_not_valid": "whitepaper_link_not_valid", "whitepaper_link_is_blank": "whitepaper_link_is_blank", "additional_url_link_is_required": "additional_url_link_is_required", "additional_url_link_not_valid": "additional_url_link_not_valid", "additional_url_link_is_blank": "additional_url_link_is_blank", "explorer_url_link_is_required": "explorer_url_link_is_required", "explorer_url_link_not_valid": "explorer_url_link_not_valid", "explorer_url_link_is_blank": "explorer_url_link_is_blank", "min_1_explorer_url_required": "min_1_explorer_url_required", "explorer_urls_is_required": "explorer_urls_is_required", "chain_name_min_1_error": "chain_name_min_1_error", "chain_name_max_50_error": "chain_name_max_50_error", "chain_name_is_required": "chain_name_is_required", "chain_name_is_blank": "chain_name_is_blank", "chain_icon_link_is_required": "chain_icon_link_is_required", "chain_icon_link_not_valid": "chain_icon_link_not_valid", "chain_icon_link_is_blank": "chain_icon_link_is_blank", "chain_address_is_required": "chain_address_is_required", "chain_address_is_blank": "chain_address_is_blank", "coin_listed": "coin_listed", "about_is_required": "about_is_required", "about_is_blank": "about_is_blank", "landing_url_link_is_required": "landing_url_link_is_required", "landing_url_link_not_valid": "landing_url_link_not_valid", "landing_url_link_is_blank": "landing_url_link_is_blank", "login_url_link_is_required": "login_url_link_is_required", "login_url_link_not_valid": "login_url_link_not_valid", "login_url_link_is_blank": "login_url_link_is_blank", "fees_url_link_is_required": "fees_url_link_is_required", "fees_url_link_not_valid": "fees_url_link_not_valid", "fees_url_link_is_blank": "fees_url_link_is_blank", "api_base_endpoint_link_is_required": "api_base_endpoint_link_is_required", "api_base_endpoint_link_not_valid": "api_base_endpoint_link_not_valid", "api_base_endpoint_link_is_blank": "api_base_endpoint_link_is_blank", "market_endpoint_is_required": "market_endpoint_is_required", "market_endpoint_is_blank": "market_endpoint_is_blank", "ticker_endpoint_is_required": "ticker_endpoint_is_required", "ticker_endpoint_is_blank": "ticker_endpoint_is_blank", "kline_endpoint_is_required": "kline_endpoint_is_required", "kline_endpoint_is_blank": "kline_endpoint_is_blank", "info_endpoint_is_required": "info_endpoint_is_required", "info_endpoint_is_blank": "info_endpoint_is_blank", "twitter_url_link_is_required": "twitter_url_link_is_required", "twitter_url_link_not_valid": "twitter_url_link_not_valid", "twitter_url_link_is_blank": "twitter_url_link_is_blank", "telegram_url_link_is_required": "telegram_url_link_is_required", "telegram_url_link_not_valid": "telegram_url_link_not_valid", "telegram_url_link_is_blank": "telegram_url_link_is_blank", "pair_format_is_required": "pair_format_is_required", "pair_format_is_blank": "pair_format_is_blank", "failed_to_adding_new_coin": "failed_to_adding_new_coin", "exchange_list": "exchange_list", "exchange_details": "exchange_details", "exchange_market_list_fetched": "Exchange market list fetched successfully", "failed_to_fetch_exchange_market_list": "Failed to fetch exchange market list", "exchange_markets_fetched": "Exchange markets fetched successfully", "failed_to_fetch_exchange_markets": "Failed to fetch exchange markets", "recently_added_coins_fetched": "recently_added_coins_fetched", "trending_coins_fetched": "trending_coins_fetched", "project_stats_retrieved": "Project statistics retrieved successfully", "failed_to_fetch_project_stats": "Failed to fetch project statistics", "failed_to_search": "failed_to_search", "search_results": "search_results", "market_list": "market_list", "failed_to_fetch_tweet_id": "failed_to_fetch_tweet_id", "coin_chart_data": "coin_chart_data", "coin_not_valid": "coin_not_valid", "coins_fetched": "coins_fetched", "coin_detail": "coin_detail", "exchange_not_found": "exchange_not_found", "failed_to_fetch_exchange_details": "failed_to_fetch_exchange_details", "failed_to_fetch_market_list": "failed_to_fetch_market_list", "dex_protocols_list": "dex_protocols_list", "dex_protocol_details": "dex_protocol_details", "dex_protocol_not_found": "dex_protocol_not_found", "dex_pairs_list": "dex_pairs_list", "image_dimensions_must_be_64x64_pixels": "image_dimensions_must_be_64x64_pixels", "failed_to_fetch_dex_pairs": "failed_to_fetch_dex_pairs", "exchange_is_required": "exchange_is_required", "coin_id_is_required": "coin_id_is_required", "failed_to_sync_historical_data": "failed_to_sync_historical_data", "interval_is_required": "interval_is_required", "historical_data_synced": "historical_data_synced", "interval_not_valid": "interval_not_valid"}