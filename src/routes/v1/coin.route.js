const coinController = require('../../controllers/coin.controller');

module.exports = (app, prisma) => {
    const router = require("express").Router();

    router.get("/", async (req, res) => {
        const finalOut = await coinController.getCoins(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/trending", async (req, res) => {
        const finalOut = await coinController.getTrendingCoins(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/recently-added", async (req, res) => {
        const finalOut = await coinController.getRecentlyAddedCoins(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/:slug", async (req, res) => {
        const finalOut = await coinController.getCoinDetails(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/:slug/markets", async (req, res) => {
        const finalOut = await coinController.getCoinMarkets(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.post('/list', async (req, res) => {
        console.log('fhdsgblb')
        const response = await coinController.getCoinList(prisma);
        res.json(response);
    });

    app.use('/api/v1/coin', router);
}