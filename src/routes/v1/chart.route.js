const chartController = require('../../controllers/chart.controller');


module.exports = (app, prisma) => {
    const router = require("express").Router();

    router.get("/:slug", async (req, res) => {
        const finalOut = await chartController.getChartData(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/kline/:slug", async (req, res) => {
        const finalOut = await chartController.getKLineData(req, prisma);
        return res.status(200).send(finalOut);
    });

    app.use('/api/v1/chart', router);
}
