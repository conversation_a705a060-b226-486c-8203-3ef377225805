const sponsoredController = require('../../controllers/sponsored.controller');


module.exports = (app, prisma) => {
    const router = require("express").Router();

    router.get("/dashboard_coins", async (req, res) => {
        const finalOut = await sponsoredController.getDashboardSponsoredCoins(prisma);
        return res.status(200).send(finalOut);
    });

    app.use('/api/v1/sponsor', router);
}
