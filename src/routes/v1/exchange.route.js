const exchangeController = require("../../controllers/exchange.controller");

module.exports = (app, prisma) => {
    const router = require("express").Router();

    router.get("/", async (req, res) => {
        const finalOut = await exchangeController.getExchanges(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/dex", async (req, res) => {
        const finalOut = await exchangeController.getDexProtocols(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/dex/pairs", async (req, res) => {
        const finalOut = await exchangeController.getDexPairsByProtocol(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/dex/:slug", async (req, res) => {
        const finalOut = await exchangeController.getDexProtocolBySlug(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/by-coin/:slug", async (req, res) => {
        const finalOut = await exchangeController.getExchangesByCoinSlug(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/:slug/markets", async (req, res) => {
        const finalOut = await exchangeController.getExchangeMarketsBySlug(req, prisma);
        return res.status(200).send(finalOut);
    });

    router.get("/:slug", async (req, res) => {
        const finalOut = await exchangeController.getExchangeDetails(req, prisma);
        return res.status(200).send(finalOut);
    });

    app.use('/api/v1/exchange', router);
}