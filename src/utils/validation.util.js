const Joi = require('joi');
const joiUtils = require('./joi.validation');
const path = require("path");
const fs = require("node:fs");
const i18n = require("../configs/i18n.config");

exports.validateExchangeForm = () => {
    return Joi.object({
        exchange_detail: Joi.object({
            name: joiUtils.getExchangeNameValidation(),
            logo_url: joiUtils.getExchangeLogoValidation(),
            exchange_type: joiUtils.getExchangeTypeValidation(),
            about: joiUtils.getStringValidate(true, 'about'),
            launch_date: joiUtils.getDateValidation(true, 'launch_date'),
            slug: joiUtils.getSlugValidation(),
        }).required(),
        metadata: Joi.object({
            landing_page_url: joiUtils.getLinkValidate(true, 'landing_url'),
            login_url: joiUtils.getLinkValidate(true, 'login_url'),
            fees_url: joiUtils.getLinkValidate(true, 'fees_url'),
            api_base_endpoint: joiUtils.getLinkValidate(true, 'api_base_endpoint'),
            market_endpoint: joiUtils.getStringValidate(true, 'market_endpoint'),
            ticker_endpoint: joiUtils.getStringValidate(true, 'ticker_endpoint'),
            kline_endpoint: joiUtils.getStringValidate(true, 'kline_endpoint'),
            twitter_url: joiUtils.getLinkValidate(true, 'twitter_url'),
            telegram_url: joiUtils.getLinkValidate(true, 'telegram_url'),
            pair_format: joiUtils.getStringValidate(true, 'pair_format'),
            api_response_format: joiUtils.getApiResponseFormatValidation(),
        }).required(),
    });
}

exports.checkUploadedExchangeIconFile = (image, allow_file_size, isGifAllow) => {
    if (image) {
        const allow_files = isGifAllow ? ['png', 'jpeg', 'jpg', 'gif'] : ['png', 'jpeg', 'jpg',];
        const allow_file_types = isGifAllow ? ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'] : ['image/png', 'image/jpeg', 'image/jpg'];
        const file_extension = image.originalname.slice(
            ((image.originalname.lastIndexOf('.') - 1) >>> 0) + 2
        );
        if (!allow_files.includes(file_extension) || !allow_file_types.includes(image.mimetype)) {
            return 1;
        }
        if ((image.size / (1024 * 1024)) > allow_file_size) {
            return 2;
        }
        return 0;
    } else {
        return 3;
    }
}

exports.checkExchangeFileIsExist = (file) => {
    const fileDir = path.join(__dirname, '../../public/data/icons/exchanges');
    const filePath = path.join(fileDir, file);
    return fs.existsSync(filePath);
}

exports.validateCoinForm = () => {
    return Joi.object({
        coin_details: Joi.object({
            name: joiUtils.getCoinNameValidation(),
            symbol: joiUtils.getCoinSymbolValidation(),
            logo_url: joiUtils.getCoinLogoValidation(),
            launch_date: joiUtils.getDateValidation(true, 'launch_date'),
            slug: joiUtils.getSlugValidation(),
            description: joiUtils.getStringValidate(true, 'description'),
        }).required(),
        
        supply_info: Joi.object({
            max_supply_infinite: joiUtils.getBoolValidate(true, 'max_supply_infinite'),
            max_supply: Joi.when('max_supply_infinite', {
                is: true,
                then: Joi.string().allow('').required(),
                otherwise: Joi.string().required().messages({
                    'any.required': i18n.__('max_supply_is_required'),
                    'string.empty': i18n.__('max_supply_is_blank'),
                }),
            }),
        }).required(),
        
        supply_data_url: joiUtils.getLinkValidate(false, 'supply_data_url'),
        
        website: Joi.object({
            website: joiUtils.getLinkValidate(true, 'website'),
            whitepaper: joiUtils.getLinkValidate(false, 'whitepaper'),
            additional_urls: Joi.array().items(joiUtils.getLinkValidate(false, 'additional_url')).optional(),
        }).required(),
        
        explorer_urls: Joi.array().items(
            joiUtils.getLinkValidate(true, 'explorer_url')
        ).min(1).required().messages({
            'array.min': i18n.__('min_1_explorer_url_required'),
            'any.required': i18n.__('explorer_urls_is_required'),
        }),
        
        contracts: Joi.array().items(
            Joi.object({
                name: joiUtils.getChainNameValidation(),
                icon: joiUtils.getLinkValidate(true, 'chain_icon'),
                address: joiUtils.getStringValidate(true, 'chain_address'),
            }).optional(),
        ).optional(),
        
        social_urls: joiUtils.getSocialUrlValidation(),
    });
}

exports.checkUploadedCoinIconFile = (image, allow_file_size, isGifAllow) => {
    if (image) {
        const allow_files = isGifAllow ? ['png', 'jpeg', 'jpg', 'gif'] : ['png', 'jpeg', 'jpg',];
        const allow_file_types = isGifAllow ? ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'] : ['image/png', 'image/jpeg', 'image/jpg'];
        const file_extension = image.originalname.slice(
            ((image.originalname.lastIndexOf('.') - 1) >>> 0) + 2
        );
        if (!allow_files.includes(file_extension) || !allow_file_types.includes(image.mimetype)) {
            return 1;
        }
        if ((image.size / (1024 * 1024)) > allow_file_size) {
            return 2;
        }
        return 0;
    } else {
        return 3;
    }
}

exports.checkCoinFileIsExist = (file) => {
    const fileDir = path.join(__dirname, '../../public/data/icons/coins');
    const filePath = path.join(fileDir, file);
    return fs.existsSync(filePath);
}