const Joi = require("joi");
const i18n = require("../configs/i18n.config");

exports.getStringValidate = (isRequired = true, name) => {
    if (isRequired) {
        return Joi.string().messages({
            'any.required': i18n.__(`${name}_is_required`),
            'string.empty': i18n.__(`${name}_is_blank`),
        });
    }
    return Joi.string().allow('').messages({
        'any.required': i18n.__(`${name}_is_required`),
    });
}

exports.getNumberValidate = (isRequired = true, name) => {
    if (isRequired) {
        return Joi.number().integer().messages({
            'any.required': i18n.__(`${name}_is_required`),
            'number.empty': i18n.__(`${name}_is_blank`),
        });
    }
    return Joi.number().integer().allow(0).messages({
        'any.required': i18n.__(`${name}_is_required`),
        'number.empty': i18n.__(`${name}_is_blank`),
    });
}

exports.getBoolValidate = (isRequired = true, name) => {
    if (isRequired) {
        return Joi.bool().required().messages({
            'any.required': i18n.__(`${name}_is_required`),
            'boolean.empty': i18n.__(`${name}_is_blank`),
            'boolean.base': i18n.__(`${name}_not_valid`),
        });
    }
    return Joi.bool().required().allow(false).messages({
        'any.required': i18n.__(`${name}_is_required`),
        'boolean.empty': i18n.__(`${name}_is_blank`),
        'boolean.base': i18n.__(`${name}_not_valid`),
    });
}

exports.getExchangeNameValidation = () => {
    return Joi.string().trim().regex(/^[a-zA-Z0-9 ]+$/).min(2).max(30).messages({
        'string.min': i18n.__('name_min_2_error'),
        'string.max': i18n.__('name_max_30_error'),
        'any.required': i18n.__('name_is_required'),
        'string.pattern.base': i18n.__('name_not_valid_string'),
        'string.empty': i18n.__('name_is_blank'),
    });
}

exports.getExchangeTypeValidation = () => {
    return Joi.string().valid('CEX', 'DEX').required();
}

exports.getExchangeLogoValidation = () => {
    return Joi.string().pattern(/^exchange-\d+-\d+\.[a-zA-Z0-9]+$/).required().messages({
        'any.required': i18n.__('logo_is_required'),
        'string.pattern.base': i18n.__('logo_not_valid'),
        'string.empty': i18n.__('logo_is_blank'),
    });
}

exports.getLinkValidate = (isRequired = true, name) => {
    if (isRequired) {
        return Joi.string().uri().required().messages({
            'any.required': i18n.__(`${name}_link_is_required`),
            'string.pattern.base': i18n.__(`${name}_link_not_valid`),
            'string.empty': i18n.__(`${name}_link_is_blank`),
        });
    }
    return Joi.string().uri().allow('').messages({
        'any.required': i18n.__(`${name}_link_is_required`),
        'string.pattern.base': i18n.__(`${name}_link_not_valid`),
        'string.empty': i18n.__(`${name}_link_is_blank`),
    });
}

exports.getEpochValidate = (isRequired = true, name = 'epoch') => {
    if (isRequired) {
        return Joi.date().timestamp('unix').required().messages({
            'any.required': i18n.__(`${name}_is_required`),
            'number.empty': i18n.__(`${name}_is_blank`),
            'date.min': i18n.__(`${name}_too_old`),
            'date.max': i18n.__(`${name}_too_future`),
            'date.format': i18n.__(`${name}_format_error`),
        });
    }
    return Joi.date().timestamp('unix').allow('').messages({
        'any.required': i18n.__(`${name}_is_required`),
        'number.empty': i18n.__(`${name}_is_blank`),
        'date.min': i18n.__(`${name}_too_old`),
        'date.max': i18n.__(`${name}_too_future`),
        'date.format': i18n.__(`${name}_format_error`),
    });
}

exports.getCountryValidation = () => {
    return Joi.string().min(1).max(100).required().messages({
        'string.base': 'Country should be a string',
        'string.empty': 'Country cannot be empty',
        'string.min': 'Country should be at least 1 character long',
        'string.max': 'Country should be less than or equal to 100 characters',
        'any.required': 'Country is required'
    });
}

exports.getNetworkValidate = () => {
    const validNetworks = [
        'Bitcoin', 'Ethereum', 'Binance', 'Solana', 'Cardano',
        'Ripple', 'Polkadot', 'Dogecoin', 'Avalanche', 'Polygon',
        'TRON', 'Cosmos', 'Chainlink', 'Uniswap', 'Stellar',
        'Algorand', 'NEAR Protocol', 'Fantom', 'Harmony', 'Hedera'
    ];

    return Joi.array()
        .items(
            Joi.string()
                .valid(...validNetworks)
                .optional()
                .messages({
                    'string.base': i18n.__('network_must_be_string'),
                    'any.required': i18n.__('network_is_required'),
                    'any.only': i18n.__('network_not_supported')
                })
        )
        .min(1)
        .unique()
        .required()
        .messages({
            'array.base': i18n.__('networks_must_be_array'),
            'array.min': i18n.__('min_1_network_select'),
            'array.unique': i18n.__('networks_must_be_unique'),
            'any.required': i18n.__('network_is_required')
        });
}

exports.getContractAddressValidate = () => {
    return Joi.string().allow('').messages({
        'any.required': i18n.__('address_is_required'),
        'string.pattern.base': i18n.__('address_not_valid'),
        'string.empty': i18n.__('address_is_blank'),
    });
}

exports.getSupportedFiatValidate = () => {
    return Joi.array().items(Joi.string()).min(1).required().messages({
        'any.required': i18n.__('fiat_is_required'),
        'string.empty': i18n.__('fiat_is_blank'),
        'array.base': i18n.__(`fiat_is_blank`),
        'array.min': i18n.__('min_1_fiat_select'),
    });
}


exports.getSlugValidation = () => {
    return Joi.string().pattern(/^[a-z0-9]+(?:-[a-z0-9]+)*$/).required().messages({
        'string.base': 'Slug should be a string',
        'string.empty': 'Slug cannot be empty',
        'string.pattern.base': 'Slug is not valid. It must consist of lowercase letters, numbers, and hyphens, and cannot start or end with a hyphen or have consecutive hyphens.',
        'any.required': 'Slug is required',
    });
}

exports.getExchangeServiceOfferValidate = () => {
    return Joi.array().items(
        Joi.string().valid('STAKING', 'FARMING', 'LENDING', 'LAUNCHPAD').optional()
    ).required().messages({
        'any.required': i18n.__('offers_section_is_required'),
        'array.empty': i18n.__('offers_section_is_blank'),
        'array.includesRequiredUnknowns': i18n.__('offers_section_contains_invalid_values'),
        'string.base': i18n.__('offers_section_must_be_string')
    });
}

exports.getApiResponseFormatValidation = () => {
    return Joi.string().valid('JSON', 'CSV', 'XML').required();
}

exports.getCoinNameValidation = () => {
    return Joi.string().trim().min(1).max(100).required().messages({
        'string.min': i18n.__('coin_name_min_1_error'),
        'string.max': i18n.__('coin_name_max_100_error'),
        'any.required': i18n.__('coin_name_is_required'),
        'string.empty': i18n.__('coin_name_is_blank'),
    });
}

exports.getCoinSymbolValidation = () => {
    return Joi.string().trim().regex(/^[A-Z0-9]+$/).min(1).max(10).required().messages({
        'string.min': i18n.__('symbol_min_1_error'),
        'string.max': i18n.__('symbol_max_10_error'),
        'any.required': i18n.__('symbol_is_required'),
        'string.pattern.base': i18n.__('symbol_not_valid_format'),
        'string.empty': i18n.__('symbol_is_blank'),
    });
}

exports.getCoinLogoValidation = () => {
    return Joi.string().pattern(/^coin-\d+-\d+\.[a-zA-Z0-9]+$/).required().messages({
        'any.required': i18n.__('coin_logo_is_required'),
        'string.pattern.base': i18n.__('coin_logo_not_valid'),
        'string.empty': i18n.__('coin_logo_is_blank'),
    });
}

exports.getDateValidation = (isRequired = true, name = 'date') => {
    if (isRequired) {
        return Joi.string().isoDate().required().messages({
            'any.required': i18n.__(`${name}_is_required`),
            'string.empty': i18n.__(`${name}_is_blank`),
            'date.format': i18n.__(`${name}_format_error`),
        });
    }
    return Joi.string().isoDate().allow('').messages({
        'any.required': i18n.__(`${name}_is_required`),
        'string.empty': i18n.__(`${name}_is_blank`),
        'date.format': i18n.__(`${name}_format_error`),
    });
}

exports.getChainIdValidation = () => {
    return Joi.string().trim().regex(/^[a-z0-9]+$/).min(1).max(20).required().messages({
        'string.min': i18n.__('chain_id_min_1_error'),
        'string.max': i18n.__('chain_id_max_20_error'),
        'any.required': i18n.__('chain_id_is_required'),
        'string.pattern.base': i18n.__('chain_id_not_valid_format'),
        'string.empty': i18n.__('chain_id_is_blank'),
    });
}

exports.getChainNameValidation = () => {
    return Joi.string().trim().min(1).max(50).required().messages({
        'string.min': i18n.__('chain_name_min_1_error'),
        'string.max': i18n.__('chain_name_max_50_error'),
        'any.required': i18n.__('chain_name_is_required'),
        'string.empty': i18n.__('chain_name_is_blank'),
    });
}

exports.getSocialUrlValidation = () => {
    return Joi.object({
        Twitter: Joi.string().uri().required(),
        Telegram: Joi.string().allow('').custom((value, helpers) => {
            if (value === '') return value;
            if (!Joi.string().uri().validate(value).error) return value;
            return helpers.error('any.invalid');
        }),
    }).pattern(
        Joi.string().invalid('Twitter', 'Telegram'),
        Joi.string().uri()
    );
}
