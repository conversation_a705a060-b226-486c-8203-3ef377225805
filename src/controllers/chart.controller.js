const i18n = require("../configs/i18n.config");
const priceHistoricalModel = require("../models/priceHistorical.model");

exports.getChartData = async (req, prisma) => {
    let outRes = { status: false, message: '', data: '' };
    try {
        const { slug } = req.params;

        const coinMarket = await prisma.coins.findFirst({
            where: {
                slug: slug,
            },
            include: {
                markets_markets_base_token_idTotokens: {
                    where: {
                        is_active: true
                    },
                    take: 1,
                    select: {
                        id: true,
                        is_active: true,
                    }
                }
            }
        });

        if (!coinMarket || !coinMarket.markets_markets_base_token_idTotokens.length) {
            outRes.message = i18n.__('market_id_not_found');
            return outRes;
        }

        const marketId = coinMarket.markets_markets_base_token_idTotokens[0].id;

        if (marketId) {
            const chartData = await prisma.ohlcv_data.findMany({
                where: {
                    market_id: marketId,
                },
                select: {
                    timestamp: true,
                    open: true,
                    close: true,
                    high: true,
                    low: true,
                    volume: true
                },
                orderBy: {
                    timestamp: 'asc'
                }
            });

            outRes.status = true;
            outRes.message = i18n.__('chart_data');
            outRes.data = chartData
        } else {
            outRes.message = i18n.__('market_id_not_found');
        }
    } catch (error) {
        console.log(error);
        outRes.message = i18n.__('failed_to_fetch_chart_data');
    }
    return outRes;
};

exports.getKLineData = async (req, prisma) => {
    let outRes = { status: false, message: '', data: '' };
    try {
        const { slug } = req.params;

        const coins = await prisma.coins.findFirst({
            where: {
                slug: slug,
            },
            select: {
                id: true,
            }
        });

        if (coins) {
            const allKLine = await priceHistoricalModel.getKLineDataTrade(coins.id, '24H', 0);
            const oneDayKLine = await priceHistoricalModel.getKLineDataTrade(coins.id, '5M', 1);
            const sevenDayKLine = await priceHistoricalModel.getKLineDataTrade(coins.id, '15M', 7);
            const oneMonthKLine = await priceHistoricalModel.getKLineDataTrade(coins.id, '1H', 30);
            const oneYearKLine = await priceHistoricalModel.getKLineDataTrade(coins.id, '24H', 365);

            outRes.data = {
                'ALL': allKLine,
                '1D': oneDayKLine,
                '7D': sevenDayKLine,
                '1M': oneMonthKLine,
                '1Y': oneYearKLine,
            }
            outRes.status = true;
            outRes.message = i18n.__('coin_chart_data');
        } else {
            outRes.message = i18n.__('coin_not_valid');
        }
    } catch (e) {
        console.error(`Error fetching chart data ==> ${e}`);
        outRes.message = i18n.__('error_fetching_chart_data');
    }
    return outRes;
}