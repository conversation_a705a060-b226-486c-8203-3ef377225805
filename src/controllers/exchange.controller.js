const i18n = require("../configs/i18n.config");
const customConfig = require("../configs/custom.config");
const dexStatsModel = require('../models/dexStats.model');
const coinStatsModel = require("../models/coinStats.model");
const exchangeStatsModel = require("../models/exchangeStats.model");
const pairStatsModel = require("../models/pairStats.model");

exports.getExchanges = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const total = await prisma.exchanges.count({
            where: {is_active: true}
        });

        const exchanges = await prisma.exchanges.findMany({
            where: {
                is_active: true
            },
            select: {
                id: true,
                name: true,
                logo_url: true,
                slug: true,
                markets: {
                    select: {
                        id: true,
                        base_token_id: true,
                        quote_token_id: true,
                    }
                },
                exchange_ranking: {
                    select: {
                        rank: true
                    }
                }
            },
            orderBy: [
                {
                    exchange_ranking: {
                        rank: 'asc',
                    },
                }
            ],
            skip,
            take: limit
        });

        const exchangeList = await Promise.all(exchanges.map(async exchange => {
            const markets = exchange.markets || [];

            const uniqueCoins = new Set();

            markets.forEach(market => {
                if (market.base_token_id) uniqueCoins.add(market.base_token_id);
                if (market.quote_token_id) uniqueCoins.add(market.quote_token_id);
            });

            const data = {
                exchange_name: exchange.name,
                exchange_slug: exchange.slug,
                icon_url: `${customConfig.image.baseUrl}${customConfig.image.exchangeIconUrl}${exchange.logo_url}`,
                trading_volume_24h: '0',
                avg_liquidity: '0',
                markets_count: markets.length,
                coins_count: uniqueCoins.size,
                volume_graph_7d: []
            }

            const exchangeStats = await exchangeStatsModel.getStatsByExchange(exchange.id);

            if (!exchangeStats) {
                return data;
            }

            return {
                ...data,
                trading_volume_24h: `${exchangeStats.spot_volume ?? '0'}`,
                avg_liquidity: `${exchangeStats.avg_liquidity ?? '0'}`,
            };
        }));

        outRes.status = true;
        outRes.data = exchangeList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('exchange_list');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_exchange_list');
    }

    return outRes;
}

exports.getExchangesByCoinSlug = async (req, prisma) => {
    let outRes = {status: false, message: '', data: []};
    const {slug} = req.params;

    try {
        const coin = await prisma.coins.findFirst({
            where: {
                slug: slug,
                is_active: true
            }
        });

        if (!coin) {
            outRes.message = i18n.__('coin_not_found');
            return outRes;
        }

        // Get all markets for this coin with exchange and market stats data
        const exchangeData = await prisma.markets.findMany({
            where: {
                base_token_id: coin.id,
                is_active: true
            },
            select: {
                pair: true,
                market_stats: {
                    select: {
                        price: true,
                        volume_24h: true
                    }
                },
                exchanges: {
                    select: {
                        name: true,
                        logoUrl: true
                    }
                }
            }
        });

        // Format the response
        outRes.data = exchangeData.map(market => ({
            exchange_name: market.exchanges?.name || '',
            exchange_logo: `${customConfig.image.baseUrl}${customConfig.image.exchangeIconUrl}${market.exchanges?.logoUrl || ''}`,
            pair: market.pair,
            price: market.market_stats?.price?.toString() || '0',
            volume_24h: market.market_stats?.volume_24h?.toString() || '0'
        }));

        outRes.status = true;
        outRes.message = i18n.__('exchange_list_fetched');
    } catch (error) {
        console.error('Error fetching exchanges by coin:', error);
        outRes.message = i18n.__('failed_to_fetch_exchanges');
    }

    return outRes;
};

exports.getExchangeDetails = async (req, prisma) => {
    let outRes = {status: false, message: '', data: null};
    try {
        const {slug} = req.params;

        const exchange = await prisma.exchanges.findUnique({
            where: {slug},
            select: {
                id: true,
                name: true,
                logo_url: true,
                about: true,
                exchange_metadata: {
                    select: {
                        landing_page_url: true,
                        login_url: true,
                        fees_url: true,
                        twitter_url: true,
                        telegram_url: true,
                        trade_page_url: true,
                        refer_param: true
                    }
                }
            },
        });

        if (!exchange) {
            outRes.message = i18n.__('exchange_not_found');
            return outRes;
        }

        outRes.data = {
            name: exchange.name,
            icon_url: `${customConfig.image.baseUrl}${customConfig.image.exchangeIconUrl}${exchange.logo_url}`,
            about: exchange.about,
            urls:  exchange.exchange_metadata || {},
            trading_volume_24h: '0',
            total_assets: '0',
        };

        const exchangeStats = await exchangeStatsModel.getStatsByExchange(exchange.id);

        if (exchangeStats) {
            outRes.data = {
                ...outRes.data,
                trading_volume_24h: `${exchangeStats.spot_volume ?? '0'}`,
                total_assets: `${exchangeStats.avg_liquidity ?? '0'}`,
            };
        }

        outRes.status = true;
        outRes.message = i18n.__('exchange_details');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_exchange_details');
    }
    return outRes;
}

exports.getExchangeMarketsBySlug = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const {slug} = req.params;

        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const exchange = await prisma.exchanges.findUnique({
            where: {
                slug: slug,
            },
            select: {
                id: true,
            }
        });

        if (!exchange) {
            outRes.message = i18n.__('exchange_not_found');
            return outRes;
        }

        const total = await prisma.markets.count({
            where: {
                exchange_id: exchange.id,
                is_active: true,
                market_stats: {
                    isNot: null
                }
            }
        });

        const markets = await prisma.markets.findMany({
            where: {
                exchange_id: exchange.id,
                is_active: true,
            },
            select: {
                id: true,
                pair: true,
                base_token_id: true,
                tokens_markets_base_token_idTotokens: {
                    select: {
                        name: true,
                        symbol: true,
                        slug: true,
                        logo_url: true,
                        market_cap_ranking: {
                            select: {
                                rank: true
                            }
                        }
                    }
                },
                tokens_markets_quote_token_idTotokens: {
                    select: {
                        symbol: true,
                    }
                },
            },
            orderBy: {
                tokens_markets_base_token_idTotokens: {
                    market_cap_ranking: {
                        rank: 'asc',
                    }
                }
            },
            skip,
            take: limit
        });

        const exchangeStats = await pairStatsModel.getStatsByExchange(exchange.id);
        const coinsPrice = await coinStatsModel.fetchCoinsPrice();

        const marketList = markets.map(market => {
            const baseToken = market.tokens_markets_base_token_idTotokens;
            const quoteToken = market.tokens_markets_quote_token_idTotokens;

            const data = {
                base_currency: {
                    name: baseToken?.name || '',
                    symbol: baseToken?.symbol || '',
                    slug: baseToken?.slug || '',
                    icon_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${baseToken?.logo_url ?? ''}`
                },
                pair: market.pair,
                pair_name: `${baseToken?.symbol || ''}/${quoteToken?.symbol || ''}`,
                price: '0',
                plus2_depth: '0',
                minus2_depth: '0',
                volume: '0',
                volume_percentage: '0',
                last_updated: new Date()
            }

            const stats = exchangeStats.find(item => item.pair_id === market.id)
            const coinStats = coinsPrice.find(item => item.coin_id === market.base_token_id)

            if (!stats && !coinStats) {
                return data;
            }

            return {
                ...data,
                price: `${coinStats?.price ?? 0}`,
                plus2_depth: `${stats?.plus2_depth ?? 0}`,
                minus2_depth: `${stats?.minus2_depth ?? 0}`,
                volume: `${stats?.volume ?? 0}`,
                volume_percentage: `${stats?.volume_per ?? 0}`,
                last_updated: `${stats?.datetime ?? new Date()}`,
            }
        });

        outRes.status = true;
        outRes.data = marketList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('exchange_markets_fetched');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_exchange_markets');
    }

    return outRes;
}

exports.getDexProtocols = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const total = await prisma.dex_protocols.count({
            where: {is_active: true}
        });

        const dexProtocols = await prisma.dex_protocols.findMany({
            where: {
                is_active: true
            },
            orderBy: [
                {
                    created_at: 'desc'
                }
            ],
            skip,
            take: limit
        });

        const protocolList = dexProtocols.map(protocol => {
            return {
                id: protocol.id,
                name: protocol.name,
                slug: protocol.slug,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.dexIconUrl}${protocol.logo_url}`,
                trade_volume_24h: '0',
                market_share_percentage: '0',
                market_count: '0',
                created_at: protocol.created_at
            };
        });

        outRes.status = true;
        outRes.data = protocolList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('dex_protocols_list') || 'DEX protocols retrieved successfully';
    } catch (error) {
        console.error('Error fetching DEX protocols:', error);
        outRes.message = i18n.__('failed_to_fetch_dex_protocols') || 'Failed to fetch DEX protocols';
    }

    return outRes;
};

exports.getDexProtocolBySlug = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const {slug} = req.params;

        const dexProtocol = await prisma.dex_protocols.findFirst({
            where: {
                slug: slug,
                is_active: true
            },
        });

        if (!dexProtocol) {
            outRes.message = i18n.__('dex_protocol_not_found') || 'DEX protocol not found';
            return outRes;
        }

        outRes.status = true;
        outRes.data = {
            id: dexProtocol.id,
            name: dexProtocol.name,
            icon_url: `${customConfig.image.baseUrl}${customConfig.image.dexIconUrl}${dexProtocol.logo_url}`,
            urls: {
                landing_page_url: dexProtocol.website_url,
                fees_url: dexProtocol.decumentation_url,
                twitter_url: 'https://twitter.com/larissanetwork',
                telegram_url: 'https://t.me/larissaconnect',
            },
            trading_volume_24h: '0',
            total_assets: '0',
        };
        outRes.message = i18n.__('dex_protocol_details') || 'DEX protocol details retrieved successfully';
    } catch (error) {
        console.error('Error fetching DEX protocol details:', error);
        outRes.message = i18n.__('failed_to_fetch_dex_protocol_details') || 'Failed to fetch DEX protocol details';
    }

    return outRes;
};

exports.getDexPairsByProtocol = async (req, prisma) => {
    let outRes = { status: false, message: '', data: '' };
    try {
        const { protocol_id } = req.query;

        if (!protocol_id) {
            outRes.message = i18n.__('protocol_id_is_required') || 'Protocol ID is required';
            return outRes;
        }

        const protocol = await prisma.dex_protocols.findFirst({
            where: {
                id: protocol_id,
                is_active: true
            }
        });

        if (!protocol) {
            outRes.message = i18n.__('dex_protocol_not_found') || 'DEX protocol not found';
            return outRes;
        }

        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const total = await prisma.dex_pair.count({
            where: {
                protocol_id: protocol_id,
                is_active: true
            }
        });

        const dexPairs = await prisma.dex_pair.findMany({
            where: {
                protocol_id: protocol_id,
                is_active: true
            },
            select: {
                id: true,
                pair_address: true,
                pair_symbol: true,
                fee_tier: true,
                tokens_dex_pair_token0_idTotokens: {
                    select: {
                        id: true,
                        name: true,
                        symbol: true,
                        logo_url: true,
                        contract_address: true,
                        decimals: true
                    }
                }
            },
            orderBy: [
                {
                    created_at: 'desc'
                }
            ],
            skip,
            take: limit
        });

        const pairList = await Promise.all(dexPairs.map(async pair => {
            const token0 = pair.tokens_dex_pair_token0_idTotokens;

            const data = {
                id: pair.id,
                pair_address: pair.pair_address,
                pair_symbol: pair.pair_symbol,
                fee_tier: pair.fee_tier?.toString() || '0',
                token0: {
                    id: token0.id,
                    name: token0.name,
                    symbol: token0.symbol,
                    logo_url: `${customConfig.image.baseUrl}${customConfig.image.tokenIconUrl}${token0.logo_url}`,
                    address: pair.contract_address,
                    decimals: token0?.decimals
                },
                price_usd: '0',
                change_percent_1h: '0',
                change_percent_24h: '0',
                transactions_24h: '0',
                volume_24h_usd: '0',
                liquidity_usd: '0',
                fdv: '0',
            };

            const stats = await dexStatsModel.fetchDexPairStatsByPairId(pair.id);

            if (!stats) {
                return data;
            }

            return {
                ...data,
                price_usd: `${stats?.price_usd ?? '0'}`,
                change_percent_1h: `${stats?.change_percent_1h ?? '0'}`,
                change_percent_24h: `${stats?.change_percent_24h ?? '0'}`,
                transactions_24h: `${stats?.txns ?? '0'}`,
                volume_24h_usd: `${stats?.volume_24h_usd ?? '0'}`,
                liquidity_usd: `${stats?.liquidity_usd ?? '0'}`,
            };
        }));

        outRes.status = true;
        outRes.data = pairList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('dex_pairs_list') || 'DEX pairs retrieved successfully';
    } catch (error) {
        console.error('❌ [getDexPairsByProtocol] Error fetching DEX pairs:', error);
        outRes.message = i18n.__('failed_to_fetch_dex_pairs') || 'Failed to fetch DEX pairs';
    }

    return outRes;
};