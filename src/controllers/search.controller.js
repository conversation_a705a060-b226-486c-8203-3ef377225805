const i18n = require("../configs/i18n.config");
const customConfig = require("../configs/custom.config");
const coinStatsModel = require("../models/coinStats.model");

exports.searchAll = async (req, prisma) => {
    let outRes = { status: false, message: '', data: '' };
    try {
        const { query, type } = req.query;

        const [coinResults, exchangeResults] = await Promise.all([
            prisma.coins.findMany({
                where: {
                    is_active: true,
                    OR: [
                        { name: { contains: query, mode: 'insensitive' } },
                        { symbol: { contains: query, mode: 'insensitive' } },
                    ],
                },
                select: {
                    id: true,
                    name: true,
                    symbol: true,
                    slug: true,
                    logo_url: true,
                    supply_data: {
                        select: {
                            circulating_supply: true
                        }
                    },
                }
            }),
            prisma.exchanges.findMany({
                where: {
                    is_active: true,
                    name: { contains: query, mode: 'insensitive' },
                },
                select: {
                    id: true,
                    name: true,
                    slug: true,
                    logo_url: true,
                }
            })
        ]);

        if (coinResults.length > 0) {
            const searchPromises = coinResults.map(coin =>
                prisma.coin_searches.upsert({
                    where: {
                        coin_id: coin.id
                    },
                    update: {
                        ip_address: req.clientIp,
                        platform: req.useragent?.platform,
                        user_id: req.user?.id || null,
                        created_at: new Date()
                    },
                    create: {
                        coin_id: coin.id,
                        ip_address: req.clientIp,
                        platform: req.useragent?.platform,
                        user_id: req.user?.id || null,
                        created_at: new Date()
                    }
                })
            );
            await Promise.all(searchPromises);
        }

        const coins = await Promise.all(coinResults.map(async (hit) => {
            const searchedCoin = {
                id: hit.id,
                name: hit.name,
                symbol: hit.symbol,
                slug: hit.slug,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${hit.logo_url}`,
                price: '0',
                price_change_percentage_24h: '0',
                volume_24h: '0',
                market_cap: '0'
            };

            const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(hit.id);

            if (!coinStats) {
                return searchedCoin;
            }

            return {
                ...searchedCoin,
                price: coinStats.price.toString(),
                price_change_percentage_24h: coinStats.change_percent['24h'].toString(),
                volume_24h: coinStats.volume['24h'].toString(),
                market_cap: coinStats.market_cap
            };
        }));

        // Process exchange results
        const exchanges = await Promise.all(exchangeResults.map(async (hit) => {
            const markets = await prisma.markets.findMany({
                where: {
                    exchange_id: hit.id,
                    is_active: true
                },
                select: {
                    id: true,
                    pair: true,
                    base_token_id: true,
                    quote_token_id: true,
                    market_stats: {
                        select: {
                            volume_24h: true,
                            price: true
                        }
                    }
                }
            });

            let totalVolume24h = 0;
            let totalLiquidity = 0;
            let validMarketsCount = 0;
            const uniqueCoins = new Set();

            markets.forEach(market => {
                const stats = market.market_stats;
                if (stats && stats.volume_24h) {
                    const volume = Number(stats.volume_24h);
                    totalVolume24h += volume;
                    totalLiquidity += volume;
                    validMarketsCount++;
                }

                if (market.base_token_id) uniqueCoins.add(market.base_token_id);
                if (market.quote_token_id) uniqueCoins.add(market.quote_token_id);
            });

            return {
                id: hit.id,
                name: hit.name,
                slug: hit.slug,
                icon_url: `${customConfig.image.baseUrl}${customConfig.image.exchangeIconUrl}${hit.logo_url}`,
                markets_count: markets.length,
                coins_count: uniqueCoins.size,
            };
        }));

        const sortedCoins = sortByExactMatchFirst(coins, ['name', 'symbol'], query);
        const sortedExchanges = sortByExactMatchFirst(exchanges, ['name'], query);

        let outData;

        switch (type) {
            case "all":
                const data = {
                    coins: [],
                    exchanges: [],
                }
                if (sortedCoins.length > 0) {
                    data.coins = sortedCoins.slice(0, 5);
                }
                if (sortedExchanges.length > 0) {
                    data.exchanges = sortedExchanges.slice(0, 5);
                }
                outData = data;
                break;
            case "coins":
                if (sortedCoins.length > 0) {
                    outData = sortedCoins;
                }
                break;
            case "exchanges":
                if (sortedExchanges.length > 0) {
                    outData = sortedExchanges;
                }
                break;
        }

        outRes.status = true;
        outRes.data = outData ?? '';
        outRes.message = i18n.__('search_results');
    } catch (error) {
        console.error('Error searching:', error);
        outRes.message = i18n.__('failed_to_search');
    }

    return outRes;
};

function sortByExactMatchFirst(items, fields, query) {
    const lowerQuery = query.toLowerCase();

    return items.sort((a, b) => {
        const aMatch = fields.some(field => a[field]?.toLowerCase() === lowerQuery);
        const bMatch = fields.some(field => b[field]?.toLowerCase() === lowerQuery);

        if (aMatch && !bMatch) return -1;
        if (!aMatch && bMatch) return 1;
        return 0;
    });
}