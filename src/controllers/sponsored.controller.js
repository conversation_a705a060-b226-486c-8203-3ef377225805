const i18n = require("../configs/i18n.config");
const customConfig = require("../configs/custom.config");
const coinStatsModel = require("../models/coinStats.model");
const priceHistoricalModel = require("../models/priceHistorical.model");

exports.getDashboardSponsoredCoins = async (prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {

        const now = new Date();

        const projects = await prisma.sponsered_projects.findMany({
            where: {
                sponsered_type: customConfig.sponsor.sponsored_type.DASHBOARD_TOP_SECTION,
                project_type: customConfig.sponsor.project_type.COIN,
                // from_data: { lte: now },
                // to_date: { gte: now },
            },
            select: {
                project_id: true,
            }
        });

        console.log(projects)

        if (projects && projects.length > 0) {
            const coinIds = projects.map(project => project.project_id).filter(Boolean);

            if (coinIds.length > 0) {
                const coinDetails = await Promise.all(
                    coinIds.map(async (coinId) => {
                        const coin = await prisma.coins.findUnique({
                            where: {id: coinId},
                            select: {
                                id: true,
                                name: true,
                                logo_url: true,
                                slug: true
                            }
                        });

                        if (!coin) return null;

                        const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(coinId);

                        const sevenDayKLine = await priceHistoricalModel.getKLineDataTrade(coin.id, '1H', 7);
                        const priceList7d = sevenDayKLine.map(price => (price.close.toString()));

                        return {
                            ...{
                                ...coin,
                                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
                            },
                            current_price: coinStats?.price ?? '0',
                            price_change_24h: coinStats?.change_percent["7d"],
                            chart_data: priceList7d
                        };
                    })
                );

                // Filter out null results
                outRes.data = coinDetails.filter(coin => coin !== null);
            } else {
                outRes.data = [];
            }
        } else {
            outRes.data = [];
        }

        outRes.status = true;
        outRes.message = i18n.__('coin_details');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_coin_details');
    }

    return outRes;
}