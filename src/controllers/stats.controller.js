const i18n = require("../configs/i18n.config");

exports.getProjectStats = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const coinCount = await prisma.coins.count({
            where: { is_active: true }
        });

        const exchangeCount = await prisma.exchanges.count({
            where: { is_active: true }
        });

        const coins = await prisma.coins.findMany({
            select: {
                id: true,
                name: true,
                symbol: true,
                slug: true,
                supply_data: {
                    select: {
                        circulating_supply: true
                    }
                },
                markets_markets_base_token_idTotokens: {
                    where: {
                        is_active: true
                    },
                    select: {
                        id: true,
                        market_stats: {
                            select: {
                                price: true,
                                price_change_percentage_24h: true,
                                volume_24h: true
                            }
                        }
                    }
                }
            },
            where: {
                is_active: true
            }
        });

        let totalMarketCap = 0;
        let totalVolume24h = 0;
        let previousTotalMarketCap = 0;
        let previousTotalVolume24h = 0;
        const coinStats = [];

        for (const coin of coins) {
            const markets = coin.markets_markets_base_token_idTotokens || [];
            
            let totalPrice = 0;
            let totalPriceChange = 0;
            let totalVolume = 0;
            let validMarketsCount = 0;

            markets.forEach(market => {
                const stats = market.market_stats;
                if (stats) {
                    totalPrice += Number(stats.price || 0);
                    totalPriceChange += Number(stats.price_change_percentage_24h || 0);
                    totalVolume += Number(stats.volume_24h || 0);
                    validMarketsCount++;
                }
            });

            const avgPrice = validMarketsCount ? totalPrice / validMarketsCount : 0;
            const circulatingSupply = Number(coin.supply_data?.circulating_supply || 0);
            const marketCap = circulatingSupply * avgPrice;
            const volume24h = totalVolume;

            const priceChangePercent = validMarketsCount ? totalPriceChange / validMarketsCount : 0;
            const previousPrice = avgPrice / (1 + priceChangePercent / 100);
            const previousMarketCap = circulatingSupply * previousPrice;
            const previousVolume24h = volume24h / (1 + priceChangePercent / 100);

            totalMarketCap += marketCap;
            totalVolume24h += volume24h;
            previousTotalMarketCap += previousMarketCap;
            previousTotalVolume24h += previousVolume24h;

            coinStats.push({
                coin_name: coin.name,
                coin_symbol: coin.symbol,
                coin_slug: coin.slug,
                market_cap: marketCap,
                volume_24h: volume24h,
                price_change_percentage_24h: priceChangePercent
            });
        }

        const marketCapChangePercent = previousTotalMarketCap > 0 
            ? ((totalMarketCap - previousTotalMarketCap) / previousTotalMarketCap) * 100 
            : 0;

        const volumeChangePercent = previousTotalVolume24h > 0 
            ? ((totalVolume24h - previousTotalVolume24h) / previousTotalVolume24h) * 100 
            : 0;

        const topCoins = coinStats
            .sort((a, b) => b.market_cap - a.market_cap)
            .slice(0, 2)
            .map(coin => ({
                coin_name: coin.coin_name,
                coin_symbol: coin.coin_symbol,
                coin_slug: coin.coin_slug,
                market_cap: coin.market_cap,
                dominance: totalMarketCap > 0 ? (coin.market_cap / totalMarketCap) * 100 : 0,
                volume_24h: coin.volume_24h,
                price_change_percentage_24h: coin.price_change_percentage_24h
            }));

        const stats = {
            coin_count: coinCount,
            exchange_count: exchangeCount,
            market_cap: {
                total: totalMarketCap,
                change_percentage_24h: marketCapChangePercent
            },
            volume_24h: {
                total: totalVolume24h,
                change_percentage_24h: volumeChangePercent
            },
            dominance: topCoins
        };

        outRes.status = true;
        outRes.data = stats;
        outRes.message = i18n.__('project_stats_retrieved');
    } catch (error) {
        console.error('Error fetching project stats:', error);
        outRes.message = i18n.__('failed_to_fetch_project_stats');
    }

    return outRes;
}; 