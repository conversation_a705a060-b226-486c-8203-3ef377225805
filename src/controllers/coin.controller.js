const i18n = require("../configs/i18n.config");
const customConfig = require("../configs/custom.config");
const coinStatsModel = require("../models/coinStats.model");
const priceHistoricalModel = require("../models/priceHistorical.model");
const pairStatsModel = require("../models/pairStats.model");

exports.getCoins = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const total = await prisma.coins.count({
            where: {is_active: true}
        });

        const coins = await prisma.coins.findMany({
            select: {
                id: true,
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                supply_data: {
                    select: {
                        circulating_supply: true
                    }
                }
            },
            where: {
                is_active: true
            },
            skip,
            take: limit
        });

        const coinList = await Promise.all(coins.map(async coin => {

            const sevenDayKLine = await priceHistoricalModel.getKLineDataTrade(coin.id, '1H', 7);
            const priceList7d = sevenDayKLine.map(price => (price.close.toString()));

            const circulatingSupply = Number(coin.supply_data?.circulating_supply || 0);

            const coinData = {
                coin_name: coin.name,
                coin_symbol: coin.symbol,
                coin_slug: coin.slug,
                logo_url: coin.logo_url,
                price: '0',
                price_change_percentage_1h: 0,
                price_change_percentage_24h: 0,
                price_change_percentage_7d: 0,
                volume_24h: '0',
                market_cap: '0',
                circulating_supply: circulatingSupply.toString(),
                price_list_7d: priceList7d
            };

            const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(coin.id);

            if (!coinStats) {
                return coinData;
            }

            return {
                ...coinData,
                price: coinStats.price.toString(),
                price_change_percentage_1h: coinStats.change_percent["1h"],
                price_change_percentage_24h: coinStats.change_percent["24h"],
                price_change_percentage_7d: coinStats.change_percent["7d"],
                volume_24h: coinStats.volume["24h"].toString(),
                market_cap: coinStats.market_cap.toString(),
            }
        }));

        coinList.sort((a, b) => Number(b.market_cap) - Number(a.market_cap));

        outRes.status = true;
        outRes.data = coinList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('coin_list');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_coin_list');
    }

    return outRes;
}

exports.getCoinDetails = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const {slug} = req.params;

        const coin = await prisma.coins.findUnique({
            where: {
                slug: slug,
                is_active: true
            },
            select: {
                id: true,
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                description: true,
                coins_metadata: {
                    select: {
                        website: true,
                        explorer_urls: true,
                        contracts: true,
                        social_urls: true
                    }
                },
                market_cap_ranking: {
                    select: {
                        rank: true,
                    }
                }
            }
        });

        if (!coin) {
            outRes.message = i18n.__('coin_not_found');
            return outRes;
        }

        await prisma.coin_page_view.upsert({
            where: {coin_id: coin.id},
            create: {
                coin_id: coin.id,
                ip_address: req.clientIp,
                user_agent: req.useragent?.source,
                platform: req.useragent?.platform,
                session_id: req.sessionID || null,
                user_id: req.user?.id || null,
                created_at: new Date()
            },
            update: {
                ip_address: req.clientIp,
                user_agent: req.useragent?.source,
                platform: req.useragent?.platform,
                created_at: new Date()
            }
        });

        const oneDayHL = await priceHistoricalModel.getHighAndLow(coin.id, 1);
        const oneMonthHL = await priceHistoricalModel.getHighAndLow(coin.id, 30);
        const oneYearHL = await priceHistoricalModel.getHighAndLow(coin.id, 365);
        const allTimeHL = await priceHistoricalModel.getHighAndLow(coin.id);

        const coinMetadata = {
            website: JSON.parse(coin.coins_metadata.website),
            explorerUrls: coin.coins_metadata.explorer_urls,
            contracts: JSON.parse(coin.coins_metadata.contracts),
            socialUrls: JSON.parse(coin.coins_metadata.social_urls)
        }

        const coinDetails = {
            id: coin.id,
            rank: `${coin.market_cap_ranking?.rank ?? '-'}`,
            name: coin.name,
            symbol: coin.symbol,
            slug: coin.slug,
            logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
            description: coin.description,
            price_summary: {
                "24H": oneDayHL ? oneDayHL : null,
                "1M": oneMonthHL ? oneMonthHL : null,
                "1Y": oneYearHL ? oneYearHL : null,
                "ALL": allTimeHL ? allTimeHL : null
            },
            metadata: coinMetadata
        };

        outRes.status = true;
        outRes.data = coinDetails;
        outRes.message = i18n.__('coin_details');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_coin_details');
    }

    return outRes;
}

exports.getTrendingCoins = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

        // Get all active coins with their market data
        const coins = await prisma.coins.findMany({
            select: {
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                created_at: true,
                markets_markets_base_token_idTotokens: {
                    where: {
                        is_active: true
                    },
                    select: {
                        id: true,
                        market_stats: {
                            select: {
                                price: true,
                                price_change_percentage_24h: true,
                                volume_24h: true
                            }
                        }
                    }
                }
            },
            where: {
                is_active: true
            }
        });

        // Calculate total volume for each coin and get 7-day price data
        const coinData = await Promise.all(coins.map(async coin => {
            const markets = coin.markets_markets_base_token_idTotokens || [];

            let totalVolume = 0;
            let totalPriceChange = 0;
            let validMarketsCount = 0;

            markets.forEach(market => {
                const stats = market.market_stats;
                if (stats && stats.volume_24h) {
                    totalVolume += Number(stats.volume_24h || 0);
                    totalPriceChange += Number(stats.price_change_percentage_24h || 0);
                    validMarketsCount++;
                }
            });

            const avgPriceChange = validMarketsCount ? (totalPriceChange / validMarketsCount) : 0;

            // Get 7-day price data
            const marketIds = markets.map(m => m.id);
            const sevenDayPrices = marketIds.length > 0 ? await prisma.ohlcv_data.findMany({
                where: {
                    market_id: {in: marketIds},
                    interval: 'D1',
                    timestamp: {
                        gte: sevenDaysAgo
                    }
                },
                orderBy: {
                    timestamp: 'asc'
                },
                select: {
                    close: true
                }
            }) : [];

            const priceList7d = sevenDayPrices.map(price => price.close.toString());

            return {
                name: coin.name,
                symbol: coin.symbol,
                slug: coin.slug,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
                volume_24h: totalVolume.toString(),
                price_change_percentage_24h: avgPriceChange.toString(),
                price_list_7d: priceList7d,
                total_volume: totalVolume // for sorting
            };
        }));

        // Sort by volume and take top 5
        const trendingCoins = coinData
            .filter(coin => Number(coin.volume_24h) > 0)
            .sort((a, b) => Number(b.total_volume) - Number(a.total_volume))
            .slice(0, 5)
            .map(coin => {
                const {total_volume, ...coinData} = coin;
                return coinData;
            });

        outRes.status = true;
        outRes.data = trendingCoins;
        outRes.message = i18n.__('trending_coins_fetched');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_trending_coins');
    }

    return outRes;
}

exports.getRecentlyAddedCoins = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const coins = await prisma.coins.findMany({
            select: {
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                created_at: true,
                markets_markets_base_token_idTotokens: {
                    where: {
                        is_active: true
                    },
                    select: {
                        id: true,
                        market_stats: {
                            select: {
                                price: true,
                                price_change_percentage_24h: true,
                                volume_24h: true
                            }
                        }
                    }
                }
            },
            where: {
                is_active: true
            },
            orderBy: {
                created_at: 'desc'
            },
            take: 5
        });

        // Get 7-day price data for each coin
        const coinData = await Promise.all(coins.map(async coin => {
            const markets = coin.markets_markets_base_token_idTotokens || [];

            let totalVolume = 0;
            let totalPriceChange = 0;
            let validMarketsCount = 0;

            markets.forEach(market => {
                const stats = market.market_stats;
                if (stats) {
                    totalVolume += Number(stats.volume_24h || 0);
                    totalPriceChange += Number(stats.price_change_percentage_24h || 0);
                    validMarketsCount++;
                }
            });

            const avgPriceChange = validMarketsCount ? (totalPriceChange / validMarketsCount) : 0;

            return {
                name: coin.name,
                symbol: coin.symbol,
                slug: coin.slug,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
                volume_24h: totalVolume.toString(),
                price_change_percentage_24h: avgPriceChange.toString(),
            };
        }));

        outRes.status = true;
        outRes.data = coinData;
        outRes.message = i18n.__('recently_added_coins_fetched');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_recently_added_coins');
    }

    return outRes;
}

exports.getCoinList = async (prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const coins = await prisma.coins.findMany({
            select: {
                id: true,
                name: true,
                logo_url: true,
            },
            where: {
                is_active: true
            },
            orderBy: {
                name: 'asc'
            }
        });

        console.log(coins)

        if (coins && (coins.length > 0)) {
            outRes.status = true;
            outRes.data = coins.map((item) => ({
                ...item,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${item.logo_url}`
            }));
            outRes.message = i18n.__('coin_list');
        } else {
            outRes.status = true;
            outRes.data = [];
            outRes.message = i18n.__('coins_not_found');
        }
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_coin_list');
    }

    return outRes;
}

exports.getCoinMarkets = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const {slug} = req.params;

        const page = parseInt(req.query.page, 10) > 0 ? parseInt(req.query.page, 10) : 1;
        const limit = parseInt(req.query.limit, 10) > 0 ? parseInt(req.query.limit, 10) : 20;
        const skip = (page - 1) * limit;

        const coin = await prisma.coins.findUnique({
            where: {
                slug: slug,
                is_active: true
            },
            select: {
                markets_markets_base_token_idTotokens: {
                    where: {
                        is_active: true
                    },
                    select: {
                        id: true,
                        pair: true,
                        base_token_id: true,
                        exchanges: {
                            select: {
                                id: true,
                                name: true,
                                logo_url: true,
                                slug: true,
                                exchange_metadata: {
                                    select: {
                                        trade_page_url: true,
                                        refer_param: true
                                    }
                                }
                            },
                        },
                        tokens_markets_base_token_idTotokens: {
                            select: {
                                symbol: true,
                            }
                        },
                        tokens_markets_quote_token_idTotokens: {
                            select: {
                                symbol: true,
                            }
                        }
                    },
                    skip,
                    take: limit
                },
                _count: {
                    select: {
                        markets_markets_base_token_idTotokens: {
                            where: {
                                is_active: true
                            }
                        }
                    }
                }
            }
        });

        if (!coin) {
            outRes.message = i18n.__('coin_not_found');
            return outRes;
        }

        const markets = coin.markets_markets_base_token_idTotokens || [];
        const total = coin._count.markets_markets_base_token_idTotokens;

        const exchangeStats = await pairStatsModel.getAllStats();
        const coinsPrice = await coinStatsModel.fetchCoinsPrice();

        const marketData = markets.map(market => {
            const baseCoinSymbol = market.tokens_markets_base_token_idTotokens.symbol;
            const quoteCoinSymbol = market.tokens_markets_quote_token_idTotokens.symbol;

            const data = {
                exchange: {
                    name: market.exchanges.name,
                    logo_url: `${customConfig.image.baseUrl}${customConfig.image.exchangeIconUrl}${market.exchanges.logo_url}`,
                    slug: market.exchanges.slug
                },
                pair: `${baseCoinSymbol}/${quoteCoinSymbol}`,
                pair_url: `${market.exchanges.exchange_metadata.trade_page_url}${market.pair}${market.exchanges.exchange_metadata.refer_param}`,
                price: '0',
                plus2_depth: '0',
                minus2_depth: '0',
                volume: '0',
                volume_percentage: '0',
                last_updated: new Date(),
            };

            const stats = exchangeStats.find(item => (item.pair_id === market.id) && (item.exchange_id === market.exchanges.id))
            const coinStats = coinsPrice.find(item => item.coin_id === market.base_token_id)

            if (!stats && !coinStats) {
                return data;
            }

            return {
                ...data,
                price: `${coinStats?.price ?? 0}`,
                plus2_depth: `${stats?.plus2_depth ?? 0}`,
                minus2_depth: `${stats?.minus2_depth ?? 0}`,
                volume: `${stats?.volume ?? 0}`,
                volume_percentage: `${stats?.volume_per ?? 0}`,
                last_updated: `${stats?.datetime ?? new Date()}`,
            }
        });

        outRes.status = true;
        outRes.data = marketData;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('market_list');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_market_list');
    }

    return outRes;
}

exports.getCoinsForWS = async (prisma, pageReq, limitReq) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const page = parseInt(pageReq, 10) > 0 ? parseInt(pageReq, 10) : 1;
        const limit = parseInt(limitReq, 10) > 0 ? parseInt(limitReq, 10) : 20;
        const skip = (page - 1) * limit;

        const total = await prisma.coins.count({
            where: {is_active: true}
        });

        const coins = await prisma.coins.findMany({
            select: {
                id: true,
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                supply_data: {
                    select: {
                        circulating_supply: true
                    }
                },
                market_cap_ranking: {
                    select: {
                        rank: true
                    }
                }
            },
            where: {
                is_active: true,
            },
            orderBy: [
                {
                    market_cap_ranking: {
                        rank: 'asc',
                    },
                }
            ],
            skip,
            take: limit
        });

        const coinList = await Promise.all(coins.map(async coin => {

            const sevenDayKLine = await priceHistoricalModel.getKLineDataTrade(coin.id, '1H', 7);
            const priceList7d = sevenDayKLine.map(price => (price.close.toString()));

            const circulatingSupply = Number(coin.supply_data?.circulating_supply || 0);

            const coinData = {
                coin_name: coin.name,
                coin_symbol: coin.symbol,
                coin_slug: coin.slug,
                logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
                price: '0',
                price_change_percentage_1h: 0,
                price_change_percentage_24h: 0,
                price_change_percentage_7d: 0,
                volume_24h: '0',
                market_cap: '0',
                circulating_supply: circulatingSupply.toString(),
                price_list_7d: priceList7d
            };

            const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(coin.id);

            if (!coinStats) {
                return coinData;
            }

            return {
                ...coinData,
                price: coinStats.price.toString(),
                price_change_percentage_1h: coinStats.change_percent["1h"],
                price_change_percentage_24h: coinStats.change_percent["24h"],
                price_change_percentage_7d: coinStats.change_percent["7d"],
                volume_24h: coinStats.volume["24h"].toString(),
                market_cap: coinStats.market_cap.toString(),
            }
        }));

        outRes.status = true;
        outRes.data = coinList;
        outRes.pagination = {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
        };
        outRes.message = i18n.__('coin_list');
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_fetch_coin_list');
    }

    return outRes;
}

// Type 0 = trending, 1 = new,
exports.getCoinsByType = async (prisma, type, limit) => {
    let outRes = {status: false, message: "", data: ""};
    try {
        const queryOptions = {
            where: {
                is_active: true,
                trending_coins: {
                    isNot: null,
                }
            },
            select: {
                id: true,
                name: true,
                symbol: true,
                slug: true,
                logo_url: true,
                trending_coins: {
                    select: {
                        score: true,
                        rank: true
                    }
                }
            },
            orderBy: [
                {
                    trending_coins: {
                        score: 'asc',
                    },
                },
                {
                    trending_coins: {
                        rank: 'asc',
                    },
                },
            ],
            take: limit,
        };

        if (type === 1) {
            queryOptions.orderBy = {
                created_at: "desc",
            };
        }

        const coins = await prisma.coins.findMany(queryOptions);

        const coinList = await Promise.all(
            coins.map(async (coin) => {
                const sevenDayKLine = await priceHistoricalModel.getKLineDataTrade(
                    coin.id,
                    "1H",
                    7
                );
                const priceList7d = sevenDayKLine.map((price) =>
                    price.close.toString()
                );

                const coinData = {
                    coin_name: coin.name,
                    coin_symbol: coin.symbol,
                    coin_slug: coin.slug,
                    logo_url: `${customConfig.image.baseUrl}${customConfig.image.coinIconUrl}${coin.logo_url}`,
                    price: "0",
                    price_change_percentage_24h: 0,
                    price_list_7d: priceList7d,
                };

                const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(coin.id);

                if (!coinStats) {
                    return coinData;
                }

                return {
                    ...coinData,
                    price: coinStats.price.toString(),
                    price_change_percentage_24h: coinStats.change_percent["24h"],
                };
            })
        );

        coinList.sort((a, b) => Number(b.price) - Number(a.price));

        outRes.status = true;
        outRes.data = coinList;
        outRes.message = i18n.__("coins_fetched");
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__("failed_to_fetch_trending_coins");
    }

    return outRes;
};

exports.coinDetails = async (prisma, coinId) => {
    const outRes = {status: false, message: '', data: ''};

    const format = {
        price: '0',
        price_change: '0',
        volume_24h: '0',
        market_cap: '0',
        circulating_supply: '0',
        total_supply: '0',
        max_supply: '0',
        max_is_infinite: true,
    }
    try {
        const supply = await prisma.supply_data.findFirst({
            where: {
                token_id: coinId,
            },
        });

        if (supply) {
            format.circulating_supply = `${supply.circulating_supply}`;
            format.total_supply = `${supply.total_supply}`;
            format.max_supply = `${supply.max_supply}`;
            format.max_is_infinite = supply.max_supply_infinite;
        }

        const coinStats = await coinStatsModel.fetchCoinStatsByCoinId(coinId);

        if (coinStats) {
            format.price = `${coinStats.price}`;
            format.price_change = `${coinStats.change_percent["24h"]}`;
            format.volume_24h = `${coinStats.volume["24h"]}`;
            format.market_cap = `${coinStats.market_cap}`;
        }

        outRes.status = true;
        outRes.message = i18n.__('coin_detail');
        outRes.data = format;
    } catch (e) {
        outRes.message = i18n.__('failed_to_fetch_coin_detail')
    }
    return outRes;
}