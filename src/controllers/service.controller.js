const ExchangeManager = require('../services/ExchangeManager');
const i18n = require("../configs/i18n.config");

exports.syncHistoricalData = async (req, prisma) => {
    let outRes = {status: false, message: '', data: ''};
    try {
        const exchange = req.query.exchange;
        const coinID = req.query.coin;
        const interval = req.query.interval;

        if (!exchange) {
            outRes.message = i18n.__('exchange_is_required')
            return outRes;
        }

        if (!coinID) {
            outRes.message = i18n.__('coin_id_is_required')
            return outRes;
        }

        const existingExchange = await prisma.exchanges.findUnique({
            where: {
                name: exchange,
            }
        });

        if (!existingExchange) {
            outRes.message = i18n.__('exchange_not_found')
            return outRes;
        }

        const existingCoin = await prisma.coins.findUnique({
            where: {
                id: coinID,
            }
        });

        if (!existingCoin) {
            outRes.message = i18n.__('coin_not_found')
            return outRes;
        }

        const allowedIntervals = ['5M', '15M', '1H', '24H'];

        if (!interval) {
            outRes.message = i18n.__('interval_is_required')
            return outRes;
        }

        if (!allowedIntervals.includes(interval)) {
            outRes.message = i18n.__('interval_not_valid')
            return outRes;
        }

        const manager = new ExchangeManager(prisma);
        await manager.initialize().catch(console.error);

        const isSynced = await manager.syncHistoricalData(exchange, coinID, interval);

        if (isSynced) {
            outRes.status = true;
            outRes.message = i18n.__('historical_data_synced');
        } else {
            outRes.status = false;
            outRes.message = i18n.__('historical_data_syncing_failed');
        }
    } catch (error) {
        console.error(error);
        outRes.message = i18n.__('failed_to_sync_historical_data');
    }

    return outRes;
}