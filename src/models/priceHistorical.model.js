const mongoDB = require('../engine/db/mongo/mongoDB');

exports.insertPriceDataBulk = async (data, coinID) => {
    try {
        const collectionName = `coin_raw_data_${coinID}`;
        const db = await mongoDB.getInstance();
        const CHUNK_SIZE = 500;
        for (let i = 0; i < data.length; i += CHUNK_SIZE) {
            const chunk = data.slice(i, i + CHUNK_SIZE);

            await db.collection(collectionName).insertMany(chunk);
        }
    } catch (e) {
        console.error('Error on insertPriceData ====>', data);
        if(e) console.error(e);
    }
}

exports.insertPriceData = async (price, time, volume, cirSupply, coinID) => {
    try {
        const collectionName = `coin_raw_data_${coinID}`;
        const db = await mongoDB.getInstance();
        await db.collection(collectionName).insertOne({
            price: price,
            volume: volume,
            cir_supply: cirSupply,
            time: time,
        });
    } catch (e) {
        console.error('Error on coin_raw_data ====>', price, time, volume, cirSupply, coinID);
        if(e) console.error(e);
    }
}

exports.buildKLineData = async (coinID, interval, intervalMinutes) => {
    try {
        const db = await mongoDB.getInstance();
        const collectionName = `coin_raw_data_${coinID}`;
        const collectionName2 = `coin_kLine_${interval}_${coinID}`;

        const result = await db.collection(collectionName).aggregate([
            {
                $addFields: {
                    roundedDate: {
                        $toDate: {
                            $subtract: [
                                {$toLong: "$time"},
                                {$mod: [{$toLong: "$time"}, 1000 * 60 * intervalMinutes]}
                            ]
                        }
                    }
                }
            },
            {
                $group: {
                    _id: "$roundedDate",
                    date_time: {$first: "$roundedDate"},
                    open: {$first: "$price"},
                    close: {$last: "$price"},
                    high: {$max: "$price"},
                    low: {$min: "$price"},
                    volume: {$sum: "$volume"},
                    market_cap: {$avg: {$multiply: ["$price", "$cir_supply"]}}
                }
            },
            {
                $sort: {date_time: 1}
            },
            {
                $merge: {
                    into: collectionName2,
                    whenMatched: "replace",
                    whenNotMatched: "insert"
                }
            }
        ]).toArray();

        await result.forEach((document) => {
            const query = {interval: document.interval};
            const update = {$set: document};
            const options = {upsert: true};

            db.collection(collectionName2).updateOne(query, update, options).then();
        });
    } catch (e) {
        console.error('Error on build kLine Data ====>', coinID, interval, intervalMinutes);
        if(e) console.error(e);
    }
}

exports.getKLineDataTrade = async (coinID, interval, days) => {
    try {
        const db = await mongoDB.getInstance();
        let collectionName;
        let fromDate;

        const now = new Date();

        if (days > 0) {
            collectionName = `coin_kLine_${interval}_${coinID}`;
            fromDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
        } else {
            collectionName = `coin_kLine_${interval}_${coinID}`;
            fromDate = null;
        }

        const query = fromDate
            ? { date_time: { $gte: fromDate, $lte: now } }
            : {};

        return await db
            .collection(collectionName)
            .find(query)
            .sort({date_time: 1})
            .toArray();
    } catch (e) {
        console.error(`❌ [getKLineDataTrade] Error fetching data for coinID=${coinID}, interval=${interval}, days=${days}`);
        console.error(e);
    }

    return false;
};

exports.insertKLineDataBulk = async (data, coinID, interval) => {
    try {
        const collectionName = `coin_kLine_${interval}_${coinID}`;
        const db = await mongoDB.getInstance();
        const CHUNK_SIZE = 500;
        for (let i = 0; i < data.length; i += CHUNK_SIZE) {
            const chunk = data.slice(i, i + CHUNK_SIZE);

            const bulkOps = chunk.map(doc => {
                const date = new Date(doc.date_time); // ensure proper Date object

                return {
                    replaceOne: {
                        filter: { _id: date },
                        replacement: {
                            _id: date,
                            date_time: date,
                            open: doc.open,
                            close: doc.close,
                            high: doc.high,
                            low: doc.low,
                            volume: doc.volume,
                            market_cap: doc.market_cap
                        },
                        upsert: true
                    }
                };
            });

            await db.collection(collectionName).bulkWrite(bulkOps, { ordered: false });
        }

        console.log(`Upserted ${data.length} K-line docs into ${collectionName}`);
    } catch (e) {
        console.error('Error on insertKLineDataBulk ====>', data);
        if(e) console.error(e);
    }
}

exports.getHighAndLow = async (coinID, days = null) => {
    try {
        const db = await mongoDB.getInstance();

        let collectionName = `coin_kLine_24H_${coinID}`;
        if (days !== null && days <= 7) {
            collectionName = `coin_kLine_15M_${coinID}`;
        }

        let matchStage = {};
        if (days !== null) {
            const now = new Date();
            const from = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);
            matchStage = { date_time: { $gte: from } };
            console.log(`Match stage for last ${days} day(s):`, matchStage);
        } else {
            console.log('No date filter (days is null)');
        }

        const collection = db.collection(collectionName);

        const athDoc = await collection
            .find(matchStage)
            .sort({ high: -1 })
            .limit(1)
            .project({ high: 1, date_time: 1 })
            .toArray();

        const atlDoc = await collection
            .find(matchStage)
            .sort({ low: 1 })
            .limit(1)
            .project({ low: 1, date_time: 1 })
            .toArray();

        if(athDoc.length <= 0 && atlDoc.length <= 0) {
            return false
        }

        return {
            high: {
                price: athDoc[0]?.high ?? null,
                date: athDoc[0]?.date_time ?? null,
            },
            low: {
                price: atlDoc[0]?.low ?? null,
                date: atlDoc[0]?.date_time ?? null,
            }
        };
    } catch (e) {
        console.error("Error fetching ATH/ATL:", e);
    }
    return false;
};
