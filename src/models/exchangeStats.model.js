const mongoDB = require("../engine/db/mongo/mongoDB");

exports.insertSpotVolume = async (exchangeId, volume) => {
    try {
        const collectionName = 'exchange_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
            },
            {
                $set: {
                    spot_volume: volume,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Spot Volume ====>', exchangeId, volume);
        if (e) console.error(e);
    }
};

exports.insertAvgLiquidity = async (exchangeId, AvgLiquidity) => {
    try {
        const collectionName = 'exchange_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
            },
            {
                $set: {
                    avg_liquidity: AvgLiquidity,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Avg Liquidity ====>', exchangeId, AvgLiquidity);
        if (e) console.error(e);
    }
};

exports.insertVolumeGraph = async (exchangeId, graph) => {
    try {
        const collectionName = 'exchange_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
            },
            {
                $set: {
                    graph_7d: graph,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Volume Graph ====>', exchangeId, graph);
        if (e) console.error(e);
    }
};

exports.insertTotalAssets = async (exchangeId, totalAssets) => {
    try {
        const collectionName = 'exchange_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
            },
            {
                $set: {
                    total_assets: totalAssets,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Total Assets ====>', exchangeId, totalAssets);
        if (e) console.error(e);
    }
};

exports.getStatsByExchange = async (exchangeId) => {
    try {
        const collectionName = `exchange_stats_data`;
        const db = await mongoDB.getInstance();
        return await db.collection(collectionName).find({
            exchange_id: exchangeId,
        }).toArray();
    } catch (e) {
        console.error('Error on get Stats By Exchange exchange_stats_data ====>', exchangeId);
        if (e) console.error(e);
    }
}

exports.getAllStats = async () => {
    try {
        const collectionName = `exchange_stats_data`;
        const db = await mongoDB.getInstance();
        return await db.collection(collectionName)
            .find({})
            .sort({ spot_volume: -1 })
            .toArray();
    } catch (e) {
        console.error('Error on get All Stats ====>');
        if (e) console.error(e);
    }
}

function getTimeBucket(date = new Date()) {
    date.setSeconds(0, 0); // Round to the nearest minute
    return date;
}

