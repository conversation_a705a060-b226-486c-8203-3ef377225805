const mongoDB = require("../engine/db/mongo/mongoDB");

exports.insertDepth = async (exchangeId, pairId, plus2Depth, minus2Depth) => {
    try {
        const collectionName = 'pair_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
                pair_id: pairId,
            },
            {
                $set: {
                    plus2_depth: plus2Depth,
                    minus2_depth: minus2Depth,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Depth exchange_stats_data ====>', exchangeId, pairId, plus2Depth, minus2Depth);
        if (e) console.error(e);
    }
};

exports.insertVolume = async (exchangeId, pairId, volume) => {
    try {
        const collectionName = 'pair_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
                pair_id: pairId,
            },
            {
                $set: {
                    volume: volume,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Volume exchange_stats_data ====>', exchangeId, pairId, volume);
        if (e) console.error(e);
    }
};

exports.insertVolumePercentage = async (exchangeId, pairId, volumePer) => {
    try {
        const collectionName = 'pair_stats_data';
        const db = await mongoDB.getInstance();
        const datetime = getTimeBucket();

        await db.collection(collectionName).updateOne(
            {
                exchange_id: exchangeId,
                pair_id: pairId,
            },
            {
                $set: {
                    volume_per: volumePer,
                    datetime: datetime
                }
            },
            { upsert: true }
        );
    } catch (e) {
        console.error('Error on insert Volume Percentage exchange_stats_data ====>', exchangeId, pairId, volumePer);
        if (e) console.error(e);
    }
};

exports.getStatsByExchange = async (exchangeId) => {
    try {
        const collectionName = `pair_stats_data`;
        const db = await mongoDB.getInstance();
        return await db.collection(collectionName).find({
            exchange_id: exchangeId,
        }).toArray();
    } catch (e) {
        console.error('Error on get Stats By Exchange exchange_stats_data ====>', exchangeId);
        if (e) console.error(e);
    }
}

exports.getAllStats = async () => {
    try {
        const collectionName = `pair_stats_data`;
        const db = await mongoDB.getInstance();
        return await db.collection(collectionName).find({}).toArray();
    } catch (e) {
        console.error('Error on get All Stats ====>');
        if (e) console.error(e);
    }
}

function getTimeBucket(date = new Date()) {
    date.setSeconds(0, 0); // Round to the nearest minute
    return date;
}

