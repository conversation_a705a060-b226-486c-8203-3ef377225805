const mongoDB = require("../engine/db/mongo/mongoDB");

exports.insertDexPairStats = async (data, pairId) => {
    try {
        const collectionName = `dex_pair_stats_data_${pairId}`;
        const db = await mongoDB.getInstance();
        await db.collection(collectionName).insertOne(data);
    } catch (e) {
        console.error('Error on dex_pair_stats_data ====>', JSON.stringify(data));
        if (e) console.error(e);
    }
}

exports.fetchDexPairStatsByPairId = async (pairId) => {
    try {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
        const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

        const db = await mongoDB.getInstance();
        const collectionName = `dex_pair_stats_data_${pairId}`;
        const latest = await db.collection(collectionName)
            .find({})
            .sort({ timestamp: -1 })
            .limit(1)
            .toArray();

        if (!latest && latest.length === 0) {
            return false;
        }
        const oneHourOld = await db.collection(collectionName)
            .find({ timestamp: { $lte: oneHourAgo } })
            .sort({ timestamp: -1 })
            .limit(1)
            .toArray();

        const oneHourOldPrice = oneHourOld[0]?.price_usd || (latest[0].price_usd || 0);

        const twentyFourHourOld = await db.collection(collectionName)
            .find({ timestamp: { $lte: twentyFourHoursAgo } })
            .sort({ timestamp: -1 })
            .limit(1)
            .toArray();

        const twentyFourHourOldPrice = twentyFourHourOld[0]?.price_usd || (latest[0].price_usd || 0);
        return {
            price_usd: latest[0].price_usd,
            volume_24h_usd: latest[0].volume_24h_usd,
            liquidity_usd: latest[0].liquidity_usd,
            txns: (latest[0].txns || 0) - (twentyFourHourOld[0]?.txns || 0),
            price_change_24h: ((latest[0].price_usd - twentyFourHourOldPrice) / twentyFourHourOldPrice) * 100,
            price_change_1h: ((latest[0].price_usd - oneHourOldPrice) / oneHourOldPrice) * 100,
        };
    } catch (e) {
        console.error(e);
    }
    return false;
};

exports.insertDexProtocolStats = async (data, protocolId) => {
    try {
        const collectionName = `dex_protocol_stats_data`;
        const db = await mongoDB.getInstance();
        await db.collection(collectionName).updateOne(
            {protocol_id: protocolId}, 
            {$set: data}, 
            {upsert: true}
        );
    } catch (e) {
        console.error('Error on dex_protocol_stats_data ====>', JSON.stringify(data));
        if (e) console.error(e);
    }
}

exports.insertTokenHolders = async (tokenAddress, chainId, holdersData) => {
    try {
        const collectionName = `token_holders_${chainId}`;
        const db = await mongoDB.getInstance();
        await db.collection(collectionName).updateOne(
            {token_address: tokenAddress}, 
            {$set: holdersData}, 
            {upsert: true}
        );
    } catch (e) {
        console.error('Error on token_holders ====>', tokenAddress, holdersData);
        if (e) console.error(e);
    }
}