const mongoDB = require("../engine/db/mongo/mongoDB");

exports.insertCoinStatsData = async (data, coinID) => {
    try {
        const collectionName = `coin_stats_data`;
        const db = await mongoDB.getInstance();
        await db.collection(collectionName).updateOne({coin_id: coinID}, {$set: data}, {upsert: true});
    } catch (e) {
        console.error('Error on coin_stats_data ====>', JSON.stringify(data));
        if (e) console.error(e);
    }
}

exports.fetchCoinStatsByCoinId = async (coinID) => {
    try {
        const db = await mongoDB.getInstance();
        const collectionName = "coin_stats_data";

        return await db
            .collection(collectionName)
            .findOne({coin_id: coinID});
    } catch (e) {
        console.error(e);
    }
    return false;
};

exports.fetchCoinsPrice = async () => {
    try {
        const db = await mongoDB.getInstance();
        const collectionName = "coin_stats_data";

        return await db
            .collection(collectionName)
            .find(
                {},
                {
                    projection: {
                        coin_id: 1,
                        price: 1,
                        _id: 0,
                    },
                }
            ).toArray();
    } catch (e) {
        console.error(e);
    }
    return false;
};

exports.fetchLatestPriceChangeAllCoin = async () => {
    try {
        const db = await mongoDB.getInstance();
        const collectionName = "coin_stats_data";
        return await db
            .collection(collectionName)
            .aggregate([
                {$sort: {timestamp: -1}},
                {
                    $group: {
                        _id: "$coin_id",
                        change: {$first: "$change_percent.24h"}
                    }
                },
                {$match: {change: {$gt: 0}}}
            ]).toArray();
    } catch (e) {
        console.error(e);
    }
    return false;
};

exports.fetchLatestMarketCapPerCoin = async () => {
    try {
        const db = await mongoDB.getInstance();
        const collectionName = "coin_stats_data";
        return await db
            .collection(collectionName)
            .aggregate([
                { $sort: { timestamp: -1 } },
                {
                    $group: {
                        _id: "$coin_id",
                        market_cap: { $first: "$market_cap" }
                    }
                },
                {
                    $match: { market_cap: { $ne: null } }
                },
                {
                    $sort: { market_cap: -1 }
                }
            ]).toArray();
    } catch (e) {
        console.error(e);
    }
    return false;
};