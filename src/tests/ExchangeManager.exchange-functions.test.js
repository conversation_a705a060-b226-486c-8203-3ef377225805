const ExchangeManager = require('../services/ExchangeManager');
const ExchangeFactory = require('../services/exchang/ExchangeFactory');
const coinStatsModel = require('../models/coinStats.model');
const exchangeStatsModel = require('../models/exchangeStats.model');
const pairStatsModel = require('../models/pairStats.model');
const priceHistoricalModel = require('../models/priceHistorical.model');

// Mock dependencies
jest.mock('../services/exchang/ExchangeFactory');
jest.mock('../models/coinStats.model');
jest.mock('../models/exchangeStats.model');
jest.mock('../models/pairStats.model');
jest.mock('../models/priceHistorical.model');

describe('ExchangeManager - Exchange Functions Tests', () => {
    let exchangeManager;
    let mockPrisma;
    let mockExchange;

    beforeEach(() => {
        // Mock Prisma client
        mockPrisma = {
            exchanges: {
                findMany: jest.fn()
            },
            markets: {
                findMany: jest.fn(),
                findFirst: jest.fn(),
                upsert: jest.fn()
            },
            coins: {
                findMany: jest.fn(),
                findFirst: jest.fn()
            },
            supply_data: {
                findFirst: jest.fn()
            },
            $transaction: jest.fn()
        };

        // Mock exchange adapter
        mockExchange = {
            name: 'TestExchange',
            fetchMarkets: jest.fn(),
            fetchMarketData: jest.fn(),
            fetchHistoricalData: jest.fn(),
            fetchRawData: jest.fn(),
            fetchDepthData: jest.fn(),
            fetchOrderBook: jest.fn()
        };

        exchangeManager = new ExchangeManager(mockPrisma);
        
        // Mock ExchangeFactory
        ExchangeFactory.createExchange.mockReturnValue(mockExchange);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('fetchMarkets() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch markets with valid data', async () => {
            const mockMarkets = [
                { base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' },
                { base_token_id: 'ETH', quote_token_id: 'USDT', pair: 'ETHUSDT' },
                { base_token_id: 'ADA', quote_token_id: 'USDT', pair: 'ADAUSDT' }
            ];
            
            mockExchange.fetchMarkets.mockResolvedValue(mockMarkets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(mockExchange.fetchMarkets).toHaveBeenCalledTimes(1);
            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(1, mockMarkets);
        });

        it('should handle empty markets response', async () => {
            mockExchange.fetchMarkets.mockResolvedValue([]);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(mockExchange.fetchMarkets).toHaveBeenCalledTimes(1);
            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(1, []);
        });

        it('should handle fetchMarkets API errors', async () => {
            const apiError = new Error('Network timeout');
            mockExchange.fetchMarkets.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarkets();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync markets for TestExchange:',
                apiError
            );
        });

        it('should handle malformed markets data', async () => {
            const malformedMarkets = [
                { base_token_id: null, quote_token_id: 'USDT', pair: 'NULLUSDT' },
                { base_token_id: 'BTC', quote_token_id: null, pair: 'BTCNULL' },
                { pair: 'INCOMPLETE' } // Missing required fields
            ];
            
            mockExchange.fetchMarkets.mockResolvedValue(malformedMarkets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(1, malformedMarkets);
        });
    });

    describe('fetchMarketData() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch market data with valid ticker information', async () => {
            const mockMarketData = [
                { 
                    symbol: 'BTCUSDT', 
                    lastPrice: 50000, 
                    volume: 100.5, 
                    quoteVolume: 5025000,
                    priceChange: 1000,
                    priceChangePercent: 2.04
                },
                { 
                    symbol: 'ETHUSDT', 
                    lastPrice: 3000, 
                    volume: 500.25, 
                    quoteVolume: 1500750,
                    priceChange: -50,
                    priceChangePercent: -1.64
                }
            ];
            
            mockExchange.fetchMarketData.mockResolvedValue(mockMarketData);
            exchangeManager.updateMarketData = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarketData();

            expect(mockExchange.fetchMarketData).toHaveBeenCalledTimes(1);
            expect(exchangeManager.updateMarketData).toHaveBeenCalledWith(1, mockMarketData);
        });

        it('should handle zero volume market data', async () => {
            const mockMarketData = [
                { 
                    symbol: 'LOWVOLCOIN', 
                    lastPrice: 0.001, 
                    volume: 0, 
                    quoteVolume: 0,
                    priceChange: 0,
                    priceChangePercent: 0
                }
            ];
            
            mockExchange.fetchMarketData.mockResolvedValue(mockMarketData);
            exchangeManager.updateMarketData = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarketData();

            expect(exchangeManager.updateMarketData).toHaveBeenCalledWith(1, mockMarketData);
        });

        it('should handle fetchMarketData API failures', async () => {
            const apiError = new Error('Rate limit exceeded');
            mockExchange.fetchMarketData.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarketData();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync market data for TestExchange:',
                apiError
            );
        });
    });

    describe('fetchHistoricalData() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch historical data for valid coin and timeframe', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];
            const mockCoin = { id: 1, launch_date: new Date('2021-01-01') };
            const mockHistoricalData = [
                { 
                    timestamp: new Date('2021-01-01T00:00:00Z'), 
                    open: 29000, 
                    close: 29500, 
                    high: 30000, 
                    low: 28500, 
                    volume: 150.75 
                },
                { 
                    timestamp: new Date('2021-01-01T00:15:00Z'), 
                    open: 29500, 
                    close: 29800, 
                    high: 30200, 
                    low: 29300, 
                    volume: 200.25 
                }
            ];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(mockCoin);
            mockExchange.fetchHistoricalData.mockResolvedValue(mockHistoricalData);
            priceHistoricalModel.insertKLineDataBulk.mockResolvedValue();

            const result = await exchangeManager.syncHistoricalData('TestExchange', 1, '15M');

            expect(mockExchange.fetchHistoricalData).toHaveBeenCalledWith('BTCUSDT', mockCoin.launch_date);
            expect(priceHistoricalModel.insertKLineDataBulk).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        date_time: expect.any(String),
                        open: 29000,
                        close: 29500,
                        high: 30000,
                        low: 28500,
                        volume: 150.75,
                        market_cap: 29500
                    })
                ]),
                1,
                '15M'
            );
            expect(result).toBe(true);
        });

        it('should handle empty historical data response', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];
            const mockCoin = { id: 1, launch_date: new Date('2021-01-01') };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(mockCoin);
            mockExchange.fetchHistoricalData.mockResolvedValue([]);
            priceHistoricalModel.insertKLineDataBulk.mockResolvedValue();

            const result = await exchangeManager.syncHistoricalData('TestExchange', 1, '1H');

            expect(priceHistoricalModel.insertKLineDataBulk).toHaveBeenCalledWith([], 1, '1H');
            expect(result).toBe(true);
        });

        it('should handle fetchHistoricalData API errors', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];
            const mockCoin = { id: 1, launch_date: new Date('2021-01-01') };
            const apiError = new Error('Historical data not available');

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(mockCoin);
            mockExchange.fetchHistoricalData.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            const result = await exchangeManager.syncHistoricalData('TestExchange', 1, '1D');

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync historical data for TestExchange:',
                apiError
            );
            expect(result).toBe(false);
        });

        it('should skip processing when coin is not found', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(null);
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            const result = await exchangeManager.syncHistoricalData('TestExchange', 1, '1D');

            expect(mockExchange.fetchHistoricalData).not.toHaveBeenCalled();
            expect(consoleSpy).toHaveBeenCalledWith(
                'Skip Fetching Historical Data For Exchange TestExchange, Pair BTCUSDT'
            );
            expect(result).toBe(false);
        });

        it('should return false when exchange name does not match', async () => {
            const result = await exchangeManager.syncHistoricalData('NonExistentExchange', 1, '1D');
            expect(result).toBe(false);
        });
    });

    describe('fetchRawData() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch raw data for multiple symbols', async () => {
            const mockMarkets = [
                { id: 1, exchange_id: 1, base_token_id: 1, pair: 'BTCUSDT' },
                { id: 2, exchange_id: 1, base_token_id: 2, pair: 'ETHUSDT' }
            ];
            const mockRawData = [
                { symbol: 'BTCUSDT', price: 50000, volume: 100.5 },
                { symbol: 'ETHUSDT', price: 3000, volume: 500.25 }
            ];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchRawData.mockResolvedValue(mockRawData);
            exchangeManager.processRawData = jest.fn().mockResolvedValue();

            await exchangeManager.syncRawData();

            expect(mockExchange.fetchRawData).toHaveBeenCalledWith(['BTCUSDT', 'ETHUSDT']);
            expect(exchangeManager.processRawData).toHaveBeenCalledWith(mockRawData, mockMarkets);
        });

        it('should handle empty markets list', async () => {
            mockPrisma.markets.findMany.mockResolvedValue([]);
            mockExchange.fetchRawData.mockResolvedValue([]);
            exchangeManager.processRawData = jest.fn().mockResolvedValue();

            await exchangeManager.syncRawData();

            expect(mockExchange.fetchRawData).toHaveBeenCalledWith([]);
            expect(exchangeManager.processRawData).toHaveBeenCalledWith([], []);
        });

        it('should handle fetchRawData API errors', async () => {
            const mockMarkets = [{ id: 1, exchange_id: 1, base_token_id: 1, pair: 'BTCUSDT' }];
            const apiError = new Error('Raw data service unavailable');

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchRawData.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncRawData();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync raw data for TestExchange:',
                apiError
            );
        });
    });

    describe('fetchDepthData() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch depth data for markets with valid prices', async () => {
            const mockMarkets = [
                { id: 1, base_token_id: 1, pair: 'BTCUSDT' },
                { id: 2, base_token_id: 2, pair: 'ETHUSDT' }
            ];
            const mockCoinsPrice = [
                { coin_id: 1, price: '50000' },
                { coin_id: 2, price: '3000' }
            ];
            const mockDepthData = {
                plus2Depth: 1500.75,
                minus2Depth: 1200.50
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            coinStatsModel.fetchCoinsPrice.mockResolvedValue(mockCoinsPrice);
            mockExchange.fetchDepthData.mockResolvedValue(mockDepthData);
            pairStatsModel.insertDepth.mockResolvedValue();

            await exchangeManager.syncDepth();

            expect(mockExchange.fetchDepthData).toHaveBeenCalledTimes(2);
            expect(mockExchange.fetchDepthData).toHaveBeenNthCalledWith(1, 'BTCUSDT', '50000');
            expect(mockExchange.fetchDepthData).toHaveBeenNthCalledWith(2, 'ETHUSDT', '3000');
            expect(pairStatsModel.insertDepth).toHaveBeenCalledTimes(2);
            expect(pairStatsModel.insertDepth).toHaveBeenNthCalledWith(1, 1, 1, 1500.75, 1200.50);
            expect(pairStatsModel.insertDepth).toHaveBeenNthCalledWith(2, 1, 2, 1500.75, 1200.50);
        });

        it('should handle markets with missing coin price data', async () => {
            const mockMarkets = [
                { id: 1, base_token_id: 1, pair: 'BTCUSDT' },
                { id: 2, base_token_id: 999, pair: 'UNKNOWNUSDT' } // coin_id not in price data
            ];
            const mockCoinsPrice = [
                { coin_id: 1, price: '50000' }
                // Missing coin_id: 999
            ];
            const mockDepthData = {
                plus2Depth: 1000,
                minus2Depth: 1000
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            coinStatsModel.fetchCoinsPrice.mockResolvedValue(mockCoinsPrice);
            mockExchange.fetchDepthData.mockResolvedValue(mockDepthData);
            pairStatsModel.insertDepth.mockResolvedValue();

            await exchangeManager.syncDepth();

            expect(mockExchange.fetchDepthData).toHaveBeenNthCalledWith(1, 'BTCUSDT', '50000');
            expect(mockExchange.fetchDepthData).toHaveBeenNthCalledWith(2, 'UNKNOWNUSDT', '0');
        });

        it('should handle empty markets list', async () => {
            mockPrisma.markets.findMany.mockResolvedValue([]);

            await exchangeManager.syncDepth();

            expect(coinStatsModel.fetchCoinsPrice).not.toHaveBeenCalled();
            expect(mockExchange.fetchDepthData).not.toHaveBeenCalled();
        });

        it('should handle fetchDepthData API errors', async () => {
            const mockMarkets = [{ id: 1, base_token_id: 1, pair: 'BTCUSDT' }];
            const mockCoinsPrice = [{ coin_id: 1, price: '50000' }];
            const apiError = new Error('Depth data not available');

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            coinStatsModel.fetchCoinsPrice.mockResolvedValue(mockCoinsPrice);
            mockExchange.fetchDepthData.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncDepth();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync raw data for TestExchange:',
                apiError
            );
        });

        it('should handle null markets response', async () => {
            mockPrisma.markets.findMany.mockResolvedValue(null);

            await exchangeManager.syncDepth();

            expect(coinStatsModel.fetchCoinsPrice).not.toHaveBeenCalled();
            expect(mockExchange.fetchDepthData).not.toHaveBeenCalled();
        });
    });

    describe('fetchOrderBook() function tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should successfully fetch order book data for liquidity calculation', async () => {
            const mockMarkets = [
                { pair: 'BTCUSDT' },
                { pair: 'ETHUSDT' }
            ];
            const mockOrderBook = {
                bids: [
                    ['49000', '1.5'],
                    ['48500', '2.0'],
                    ['48000', '3.5']
                ],
                asks: [
                    ['51000', '1.2'],
                    ['51500', '2.5'],
                    ['52000', '4.0']
                ]
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(mockExchange.fetchOrderBook).toHaveBeenCalledTimes(2);
            expect(mockExchange.fetchOrderBook).toHaveBeenNthCalledWith(1, 'BTCUSDT');
            expect(mockExchange.fetchOrderBook).toHaveBeenNthCalledWith(2, 'ETHUSDT');
            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, expect.any(Number));
        });

        it('should handle empty order book (no bids/asks)', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const mockOrderBook = {
                bids: [],
                asks: []
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, 0);
        });

        it('should handle order book with only bids', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const mockOrderBook = {
                bids: [['49000', '1.0']],
                asks: []
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, 0);
        });

        it('should handle order book with only asks', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const mockOrderBook = {
                bids: [],
                asks: [['51000', '1.0']]
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, 0);
        });

        it('should handle fetchOrderBook API errors', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const apiError = new Error('Order book service down');

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockRejectedValue(apiError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.calculateAvgLiquidity();

            expect(consoleSpy).toHaveBeenCalledWith(
                '❌ Failed to calculate avg liquidity for TestExchange:',
                apiError
            );
        });

        it('should handle empty markets list for liquidity calculation', async () => {
            mockPrisma.markets.findMany.mockResolvedValue([]);

            await exchangeManager.calculateAvgLiquidity();

            expect(mockExchange.fetchOrderBook).not.toHaveBeenCalled();
            expect(exchangeStatsModel.insertAvgLiquidity).not.toHaveBeenCalled();
        });

        it('should handle null markets response for liquidity calculation', async () => {
            mockPrisma.markets.findMany.mockResolvedValue(null);

            await exchangeManager.calculateAvgLiquidity();

            expect(mockExchange.fetchOrderBook).not.toHaveBeenCalled();
            expect(exchangeStatsModel.insertAvgLiquidity).not.toHaveBeenCalled();
        });

        it('should calculate correct average liquidity with multiple pairs', async () => {
            const mockMarkets = [
                { pair: 'BTCUSDT' },
                { pair: 'ETHUSDT' }
            ];

            // Mock different order books for different pairs
            const btcOrderBook = {
                bids: [['49000', '2.0'], ['48000', '3.0']],
                asks: [['51000', '2.0'], ['52000', '3.0']]
            };
            const ethOrderBook = {
                bids: [['2900', '10.0'], ['2800', '15.0']],
                asks: [['3100', '10.0'], ['3200', '15.0']]
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook
                .mockResolvedValueOnce(btcOrderBook)
                .mockResolvedValueOnce(ethOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(mockExchange.fetchOrderBook).toHaveBeenCalledTimes(2);
            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, expect.any(Number));
        });
    });

    describe('Integration tests for multiple exchange functions', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
            exchangeManager.exchangesConfig.set(2, {
                ...mockExchange,
                name: 'SecondExchange',
                fetchMarkets: jest.fn(),
                fetchMarketData: jest.fn(),
                fetchHistoricalData: jest.fn(),
                fetchRawData: jest.fn(),
                fetchDepthData: jest.fn(),
                fetchOrderBook: jest.fn()
            });
        });

        it('should handle multiple exchanges with different response formats', async () => {
            const exchange1Markets = [
                { base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }
            ];
            const exchange2Markets = [
                { base_token_id: 'ETH', quote_token_id: 'USDT', pair: 'ETHUSDT' }
            ];

            mockExchange.fetchMarkets.mockResolvedValue(exchange1Markets);
            exchangeManager.exchangesConfig.get(2).fetchMarkets.mockResolvedValue(exchange2Markets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(mockExchange.fetchMarkets).toHaveBeenCalledTimes(1);
            expect(exchangeManager.exchangesConfig.get(2).fetchMarkets).toHaveBeenCalledTimes(1);
            expect(exchangeManager.updateMarkets).toHaveBeenCalledTimes(2);
        });

        it('should continue processing other exchanges when one fails', async () => {
            const validMarkets = [
                { base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }
            ];

            mockExchange.fetchMarkets.mockRejectedValue(new Error('Exchange 1 failed'));
            exchangeManager.exchangesConfig.get(2).fetchMarkets.mockResolvedValue(validMarkets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarkets();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync markets for TestExchange:',
                expect.any(Error)
            );
            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(2, validMarkets);
        });
    });

    describe('Edge cases and error handling', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should handle undefined/null responses from exchange functions', async () => {
            mockExchange.fetchMarkets.mockResolvedValue(undefined);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(1, undefined);
        });

        it('should handle very large datasets without memory issues', async () => {
            // Simulate large dataset
            const largeMarketData = Array.from({ length: 10000 }, (_, i) => ({
                symbol: `COIN${i}USDT`,
                lastPrice: Math.random() * 100,
                volume: Math.random() * 1000,
                quoteVolume: Math.random() * 100000
            }));

            mockExchange.fetchMarketData.mockResolvedValue(largeMarketData);
            exchangeManager.updateMarketData = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarketData();

            expect(exchangeManager.updateMarketData).toHaveBeenCalledWith(1, largeMarketData);
        });

        it('should handle concurrent calls to exchange functions', async () => {
            const mockMarkets = [{ base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }];
            const mockMarketData = [{ symbol: 'BTCUSDT', lastPrice: 50000, volume: 100, quoteVolume: 5000000 }];

            mockExchange.fetchMarkets.mockResolvedValue(mockMarkets);
            mockExchange.fetchMarketData.mockResolvedValue(mockMarketData);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();
            exchangeManager.updateMarketData = jest.fn().mockResolvedValue();

            // Simulate concurrent calls
            const promises = [
                exchangeManager.syncMarkets(),
                exchangeManager.syncMarketData()
            ];

            await Promise.all(promises);

            expect(mockExchange.fetchMarkets).toHaveBeenCalledTimes(1);
            expect(mockExchange.fetchMarketData).toHaveBeenCalledTimes(1);
        });

        it('should handle malformed timestamp data in historical data', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];
            const mockCoin = { id: 1, launch_date: new Date('2021-01-01') };
            const malformedHistoricalData = [
                {
                    timestamp: 'invalid-date',
                    open: 50000,
                    close: 51000,
                    high: 52000,
                    low: 49000,
                    volume: 100
                },
                {
                    timestamp: null,
                    open: 51000,
                    close: 52000,
                    high: 53000,
                    low: 50000,
                    volume: 150
                }
            ];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(mockCoin);
            mockExchange.fetchHistoricalData.mockResolvedValue(malformedHistoricalData);
            priceHistoricalModel.insertKLineDataBulk.mockResolvedValue();

            // Should not throw error, but handle gracefully
            await expect(exchangeManager.syncHistoricalData('TestExchange', 1, '15M')).resolves.toBe(true);
        });

        it('should handle network timeout scenarios', async () => {
            const timeoutError = new Error('ETIMEDOUT');
            timeoutError.code = 'ETIMEDOUT';

            mockExchange.fetchMarkets.mockRejectedValue(timeoutError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarkets();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync markets for TestExchange:',
                timeoutError
            );
        });

        it('should handle rate limiting scenarios', async () => {
            const rateLimitError = new Error('Rate limit exceeded');
            rateLimitError.status = 429;

            mockExchange.fetchMarketData.mockRejectedValue(rateLimitError);
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarketData();

            expect(consoleSpy).toHaveBeenCalledWith(
                'Failed to sync market data for TestExchange:',
                rateLimitError
            );
        });
    });

    describe('Performance and stress tests', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should handle rapid successive calls efficiently', async () => {
            const mockMarkets = [{ base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }];

            mockExchange.fetchMarkets.mockResolvedValue(mockMarkets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            const startTime = Date.now();

            // Make 10 rapid successive calls
            const promises = Array.from({ length: 10 }, () => exchangeManager.syncMarkets());
            await Promise.all(promises);

            const endTime = Date.now();
            const executionTime = endTime - startTime;

            // Should complete within reasonable time (adjust threshold as needed)
            expect(executionTime).toBeLessThan(5000); // 5 seconds
            expect(mockExchange.fetchMarkets).toHaveBeenCalledTimes(10);
        });

        it('should handle memory efficiently with large order books', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];

            // Create large order book
            const largeBids = Array.from({ length: 1000 }, (_, i) => [
                (50000 - i).toString(),
                (Math.random() * 10).toString()
            ]);
            const largeAsks = Array.from({ length: 1000 }, (_, i) => [
                (50001 + i).toString(),
                (Math.random() * 10).toString()
            ]);

            const largeOrderBook = {
                bids: largeBids,
                asks: largeAsks
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(largeOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, expect.any(Number));
        });
    });
});
