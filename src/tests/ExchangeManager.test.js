const ExchangeManager = require('../services/ExchangeManager');
const ExchangeFactory = require('../services/exchang/ExchangeFactory');
const coinStatsModel = require('../models/coinStats.model');
const exchangeStatsModel = require('../models/exchangeStats.model');
const pairStatsModel = require('../models/pairStats.model');
const priceHistoricalModel = require('../models/priceHistorical.model');

// Mock dependencies
jest.mock('../services/exchang/ExchangeFactory');
jest.mock('../models/coinStats.model');
jest.mock('../models/exchangeStats.model');
jest.mock('../models/pairStats.model');
jest.mock('../models/priceHistorical.model');

describe('ExchangeManager', () => {
    let exchangeManager;
    let mockPrisma;
    let mockExchange;

    beforeEach(() => {
        // Mock Prisma client
        mockPrisma = {
            exchanges: {
                findMany: jest.fn()
            },
            markets: {
                findMany: jest.fn(),
                findFirst: jest.fn(),
                upsert: jest.fn()
            },
            coins: {
                findMany: jest.fn(),
                findFirst: jest.fn()
            },
            supply_data: {
                findFirst: jest.fn()
            },
            $transaction: jest.fn()
        };

        // Mock exchange adapter
        mockExchange = {
            name: 'TestExchange',
            fetchMarkets: jest.fn(),
            fetchMarketData: jest.fn(),
            fetchHistoricalData: jest.fn(),
            fetchRawData: jest.fn(),
            fetchDepthData: jest.fn(),
            fetchOrderBook: jest.fn()
        };

        exchangeManager = new ExchangeManager(mockPrisma);
        
        // Mock ExchangeFactory
        ExchangeFactory.createExchange.mockReturnValue(mockExchange);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('initialize', () => {
        it('should initialize exchanges configuration successfully', async () => {
            const mockExchangeConfigs = [{
                id: 1,
                name: 'Binance',
                exchange_metadata: {
                    api_base_endpoint: 'https://api.binance.com',
                    market_endpoint: '/api/v3/exchangeInfo',
                    ticker_endpoint: '/api/v3/ticker/24hr',
                    kline_endpoint: '/api/v3/klines',
                    pair_format: 'BTCUSDT',
                    trade_endpoint: '/api/v3/ticker/price',
                    depth_endpoint: '/api/v3/depth'
                }
            }];

            mockPrisma.exchanges.findMany.mockResolvedValue(mockExchangeConfigs);

            await exchangeManager.initialize();

            expect(mockPrisma.exchanges.findMany).toHaveBeenCalledWith({
                where: { is_active: true },
                select: expect.any(Object)
            });
            expect(ExchangeFactory.createExchange).toHaveBeenCalledTimes(1);
            expect(exchangeManager.exchangesConfig.size).toBe(1);
        });
    });

    describe('syncMarkets', () => {
        beforeEach(async () => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should sync markets successfully', async () => {
            const mockMarkets = [
                { base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }
            ];
            
            mockExchange.fetchMarkets.mockResolvedValue(mockMarkets);
            exchangeManager.updateMarkets = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarkets();

            expect(mockExchange.fetchMarkets).toHaveBeenCalled();
            expect(exchangeManager.updateMarkets).toHaveBeenCalledWith(1, mockMarkets);
        });

        it('should handle errors during market sync', async () => {
            mockExchange.fetchMarkets.mockRejectedValue(new Error('API Error'));
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            await exchangeManager.syncMarkets();

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('Failed to sync markets'),
                expect.any(Error)
            );
        });
    });

    describe('syncMarketData', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should sync market data successfully', async () => {
            const mockMarketData = [
                { symbol: 'BTCUSDT', lastPrice: 50000, volume: 100, quoteVolume: 5000000 }
            ];
            
            mockExchange.fetchMarketData.mockResolvedValue(mockMarketData);
            exchangeManager.updateMarketData = jest.fn().mockResolvedValue();

            await exchangeManager.syncMarketData();

            expect(mockExchange.fetchMarketData).toHaveBeenCalled();
            expect(exchangeManager.updateMarketData).toHaveBeenCalledWith(1, mockMarketData);
        });
    });

    describe('syncHistoricalData', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should sync historical data successfully', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT', base_token_id: 1 }];
            const mockCoin = { id: 1, launch_date: new Date('2021-01-01') };
            const mockHistoricalData = [
                { timestamp: new Date(), open: 50000, close: 51000, high: 52000, low: 49000, volume: 100 }
            ];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockPrisma.coins.findFirst.mockResolvedValue(mockCoin);
            mockExchange.fetchHistoricalData.mockResolvedValue(mockHistoricalData);
            priceHistoricalModel.insertKLineDataBulk.mockResolvedValue();

            const result = await exchangeManager.syncHistoricalData('TestExchange', 1, '15M');

            expect(mockExchange.fetchHistoricalData).toHaveBeenCalledWith('BTCUSDT', mockCoin.launch_date);
            expect(priceHistoricalModel.insertKLineDataBulk).toHaveBeenCalled();
            expect(result).toBe(true);
        });

        it('should return false when exchange not found', async () => {
            const result = await exchangeManager.syncHistoricalData('NonExistentExchange', 1, '15M');
            expect(result).toBe(false);
        });
    });

    describe('syncRawData', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should sync raw data successfully', async () => {
            const mockMarkets = [
                { id: 1, exchange_id: 1, base_token_id: 1, pair: 'BTCUSDT' }
            ];
            const mockRawData = [
                { symbol: 'BTCUSDT', price: 50000, volume: 100 }
            ];

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchRawData.mockResolvedValue(mockRawData);
            exchangeManager.processRawData = jest.fn().mockResolvedValue();

            await exchangeManager.syncRawData();

            expect(mockExchange.fetchRawData).toHaveBeenCalledWith(['BTCUSDT']);
            expect(exchangeManager.processRawData).toHaveBeenCalledWith(mockRawData, mockMarkets);
        });
    });

    describe('syncDepth', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should sync depth data successfully', async () => {
            const mockMarkets = [
                { id: 1, base_token_id: 1, pair: 'BTCUSDT' }
            ];
            const mockCoinsPrice = [
                { coin_id: 1, price: '50000' }
            ];
            const mockDepthData = {
                plus2Depth: 1000,
                minus2Depth: 1000
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            coinStatsModel.fetchCoinsPrice.mockResolvedValue(mockCoinsPrice);
            mockExchange.fetchDepthData.mockResolvedValue(mockDepthData);
            pairStatsModel.insertDepth.mockResolvedValue();

            await exchangeManager.syncDepth();

            expect(mockExchange.fetchDepthData).toHaveBeenCalledWith('BTCUSDT', '50000');
            expect(pairStatsModel.insertDepth).toHaveBeenCalledWith(1, 1, 1000, 1000);
        });
    });

    describe('updateMarkets', () => {
        it('should update markets successfully', async () => {
            const mockCoins = [
                { id: 1, symbol: 'BTC' },
                { id: 2, symbol: 'USDT' }
            ];
            const mockMarkets = [
                { base_token_id: 'BTC', quote_token_id: 'USDT', pair: 'BTCUSDT' }
            ];

            mockPrisma.coins.findMany.mockResolvedValue(mockCoins);
            mockPrisma.$transaction.mockResolvedValue();

            await exchangeManager.updateMarkets(1, mockMarkets);

            expect(mockPrisma.coins.findMany).toHaveBeenCalled();
            expect(mockPrisma.$transaction).toHaveBeenCalled();
        });
    });

    describe('updateMarketData', () => {
        it('should update market data successfully', async () => {
            const mockMarketData = [
                { symbol: 'BTCUSDT', lastPrice: 50000, volume: 100, quoteVolume: 5000000 }
            ];
            const mockMarket = { id: 1 };

            mockPrisma.markets.findFirst.mockResolvedValue(mockMarket);
            pairStatsModel.insertVolumePercentage.mockResolvedValue();
            exchangeStatsModel.insertSpotVolume.mockResolvedValue();

            await exchangeManager.updateMarketData(1, mockMarketData);

            expect(pairStatsModel.insertVolumePercentage).toHaveBeenCalled();
            expect(exchangeStatsModel.insertSpotVolume).toHaveBeenCalledWith(1, 5000000);
        });
    });

    describe('processRawData', () => {
        it('should process raw data successfully', async () => {
            const mockRawData = [
                { symbol: 'BTCUSDT', price: 50000, volume: 100 }
            ];
            const mockMarketData = [
                { id: 1, exchange_id: 1, base_token_id: 1, pair: 'BTCUSDT' }
            ];
            const mockSupply = { circulating_supply: 19000000 };

            mockPrisma.supply_data.findFirst.mockResolvedValue(mockSupply);
            priceHistoricalModel.insertPriceData.mockResolvedValue();
            pairStatsModel.insertVolume.mockResolvedValue();

            await exchangeManager.processRawData(mockRawData, mockMarketData);

            expect(priceHistoricalModel.insertPriceData).toHaveBeenCalledWith(
                50000, expect.any(Date), 100, 19000000, 1
            );
            expect(pairStatsModel.insertVolume).toHaveBeenCalledWith(1, 1, 100);
        });
    });

    describe('calculateAvgLiquidity', () => {
        beforeEach(() => {
            exchangeManager.exchangesConfig.set(1, mockExchange);
        });

        it('should calculate average liquidity successfully', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const mockOrderBook = {
                bids: [['49000', '1.0'], ['48000', '2.0']],
                asks: [['51000', '1.0'], ['52000', '2.0']]
            };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(mockExchange.fetchOrderBook).toHaveBeenCalledWith('BTCUSDT');
            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalled();
        });

        it('should handle empty order book', async () => {
            const mockMarkets = [{ pair: 'BTCUSDT' }];
            const mockOrderBook = { bids: [], asks: [] };

            mockPrisma.markets.findMany.mockResolvedValue(mockMarkets);
            mockExchange.fetchOrderBook.mockResolvedValue(mockOrderBook);
            exchangeStatsModel.insertAvgLiquidity.mockResolvedValue();

            await exchangeManager.calculateAvgLiquidity();

            expect(exchangeStatsModel.insertAvgLiquidity).toHaveBeenCalledWith(1, 0);
        });
    });
});