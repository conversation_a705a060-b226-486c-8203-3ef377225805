const { MongoClient } = require('mongodb');
const customConfig = require('../../../configs/custom.config')

const dbConnection = function () {
    let db = null;

    async function dbConnect() {
        try {
            let url = customConfig.db.mongo.url;
            const client = new MongoClient(url);
            await client.connect();
            return client.db(customConfig.db.mongo.dbName);
        } catch (e) {
            return e;
        }
    }

    async function getObj() {
        try {
            if (db != null) {
                return db;
            } else {
                db = await dbConnect();
                return db;
            }
        } catch (e) {
            return e;
        }
    }

    return {
        getInstance: getObj
    }
}

module.exports = dbConnection();
