const express = require("express");
const cors = require("cors");
const http = require('http');
const WebSocket = require('ws');
const rateLimit = require("express-rate-limit");
const requestIp = require('request-ip');
const useragent = require('express-useragent');
const bodyParser = require('body-parser');
const customConfig = require('./configs/custom.config.js');
const i18n = require("./configs/i18n.config");
const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient({});
const app = express();
const server = http.createServer(app);

// Initialize WebSocket server
const wss = new WebSocket.Server({ server });

// Initialize WebSocket service
const WebSocketService = require('./services/websocket.service');
const wsService = new WebSocketService(wss, prisma);
wsService.initialize();

require('./crons/coin.cron').start(prisma);
require('./crons/coinStats.cron').start(prisma);
require('./crons/exchange.cron').start(prisma);
require('./crons/klineBuilder.cron').start(prisma);
require('./crons/dex.cron').start(prisma);

app.set('trust proxy', 1);

app.use((req, res, next) => {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PATCH, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'access-control-allow-origin, content-type, Authorization, Origin');
    if (req.method === 'OPTIONS') {
        return res.sendStatus(200);
    }
    next();
});

const limiter = rateLimit({
    windowMs: 60 * 1000,
    max: 1000,
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);

app.use(i18n.init);

app.use(bodyParser.json())
app.use(cors(customConfig.server.corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use(requestIp.mw());
app.use(useragent.express());

app.use(express.static("public"));
app.use('/controlcenter', express.static("controlcenter"));

require("./routes/index.route.js")(app, prisma);

server.setTimeout(customConfig.server.requestTimeOut);
server.listen(customConfig.server.port, '0.0.0.0');
console.log('listening on', customConfig.server.port);


