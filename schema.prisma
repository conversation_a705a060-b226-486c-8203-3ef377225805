generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model coins {
  id                                     String              @id @default(dbgenerated("gen_random_cuid()"))
  name                                   String
  symbol                                 String              @unique
  slug                                   String              @unique
  logo_url                               String?
  description                            String?
  launch_date                            DateTime?           @db.Date
  is_active                              Boolean?            @default(true)
  created_at                             DateTime?           @default(now()) @db.Timestamp(6)
  coin_page_view                         coin_page_view?
  coin_searches                          coin_searches?
  coins_metadata                         coins_metadata?
  market_cap_ranking                     market_cap_ranking?
  markets_markets_base_token_idTotokens  markets[]           @relation("markets_base_token_idTotokens")
  markets_markets_quote_token_idTotokens markets[]           @relation("markets_quote_token_idTotokens")
  supply_data                            supply_data?
  trending_coins                         trending_coins?
}

model exchanges {
  id                String             @id(map: "Exchange_pkey") @default(dbgenerated("gen_random_cuid()"))
  name              String             @unique(map: "exchange_name_key")
  exchange_type     ExchangeType
  logo_url          String
  slug              String             @unique(map: "Exchange_slug_key")
  createdAt         DateTime           @default(now())
  updatedAt         DateTime
  is_active         Boolean            @default(false)
  launch_date       DateTime?          @db.Date
  about             String?
  exchange_metadata exchange_metadata?
  exchange_ranking  exchange_ranking?
  markets           markets[]
}

model market_stats {
  id                          String    @id @default(dbgenerated("gen_random_cuid()"))
  market_id                   String?   @unique(map: "unique_market_id")
  price                       Decimal?  @db.Decimal(38, 18)
  volume_24h                  Decimal?  @db.Decimal(38, 18)
  price_change_percentage_24h Decimal?  @db.Decimal(10, 4)
  high_24h                    Decimal?  @db.Decimal(38, 18)
  low_24h                     Decimal?  @db.Decimal(38, 18)
  last_updated                DateTime? @db.Timestamp(6)
  markets                     markets?  @relation(fields: [market_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

model markets {
  id                                    String        @id @default(dbgenerated("gen_random_cuid()"))
  exchange_id                           String?
  base_token_id                         String?
  quote_token_id                        String?
  pair                                  String
  is_active                             Boolean?      @default(true)
  market_stats                          market_stats?
  tokens_markets_base_token_idTotokens  coins?        @relation("markets_base_token_idTotokens", fields: [base_token_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  exchanges                             exchanges?    @relation(fields: [exchange_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  tokens_markets_quote_token_idTotokens coins?        @relation("markets_quote_token_idTotokens", fields: [quote_token_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ohlcv_data                            ohlcv_data[]

  @@unique([exchange_id, pair])
}

model ohlcv_data {
  id               String        @id @default(dbgenerated("gen_random_cuid()"))
  market_id        String?
  interval         interval_enum
  timestamp        DateTime      @db.Timestamp(6)
  open             Decimal?      @db.Decimal(38, 18)
  high             Decimal?      @db.Decimal(38, 18)
  low              Decimal?      @db.Decimal(38, 18)
  close            Decimal?      @db.Decimal(38, 18)
  volume           Decimal?      @db.Decimal(38, 18)
  quote_volume     Decimal?      @db.Decimal(38, 18)
  trade_count      Int?
  base_buy_volume  Decimal?      @db.Decimal(38, 18)
  quote_buy_volume Decimal?      @db.Decimal(38, 18)
  markets          markets?      @relation(fields: [market_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([market_id, interval, timestamp])
}

model supply_data {
  token_id            String    @id
  circulating_supply  Decimal?  @db.Decimal(38, 18)
  total_supply        Decimal?  @db.Decimal(38, 18)
  max_supply          Decimal?  @db.Decimal(38, 18)
  last_updated        DateTime? @db.Timestamp(6)
  max_supply_infinite Boolean   @default(false)
  tokens              coins     @relation(fields: [token_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model token_socials {
  token_id String  @id
  twitter  String?
  telegram String?
  discord  String?
  reddit   String?
  github   String?
  tokens   tokens  @relation(fields: [token_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model tokens {
  id                                  String         @id @default(dbgenerated("gen_random_cuid()"))
  name                                String
  symbol                              String
  decimals                            Int?
  logo_url                            String?
  is_active                           Boolean?       @default(true)
  created_at                          DateTime?      @default(now()) @db.Timestamp(6)
  blockchain_id                       String?
  contract_address                    String?
  token_type                          tokentype?
  verified                            Boolean        @default(false)
  dex_pair_dex_pair_token0_idTotokens dex_pair[]     @relation("dex_pair_token0_idTotokens")
  dex_pair_dex_pair_token1_idTotokens dex_pair[]     @relation("dex_pair_token1_idTotokens")
  token_socials                       token_socials?
  blockchains                         blockchains?   @relation(fields: [blockchain_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "tokens_blockchains_id_fkey")

  @@unique([blockchain_id, contract_address], map: "tokens_blockchain_id_contract_address_uindex")
}

model exchange_metadata {
  exchange_id         String             @id
  landing_page_url    String?
  login_url           String?
  fees_url            String?
  api_base_endpoint   String?
  market_endpoint     String?
  ticker_endpoint     String?
  kline_endpoint      String?
  twitter_url         String?
  telegram_url        String?
  pair_format         String?
  api_response_format ApiResponseFormat?
  depth_endpoint      String?
  refer_param         String?
  trade_endpoint      String?
  trade_page_url      String?
  exchanges           exchanges          @relation(fields: [exchange_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model sponsered_projects {
  id             String    @id @default(dbgenerated("gen_random_cuid()"))
  project_type   Int?
  project_id     String?
  sponsered_type Int?
  from_data      DateTime? @db.Timestamp(6)
  to_date        DateTime? @db.Timestamp(6)
  created_at     DateTime? @default(now()) @db.Timestamp(6)
  updated_at     DateTime? @default(now()) @db.Timestamp(6)
}

model coin_page_view {
  coin_id    String    @id
  user_id    String?
  ip_address String?
  session_id String?
  platform   String?
  user_agent String?
  created_at DateTime? @db.Timestamp(6)
  coins      coins     @relation(fields: [coin_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model coin_searches {
  coin_id    String   @id
  user_id    String?
  ip_address String?
  platform   String?
  created_at DateTime @db.Timestamp(6)
  coins      coins    @relation(fields: [coin_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model trending_coins {
  coin_id   String    @id
  score     Decimal?  @db.Decimal(38, 18)
  rank      Int?
  update_at DateTime? @db.Timestamp(6)
  coins     coins     @relation(fields: [coin_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "trending_coins_coins_fkey")
}

model market_cap_ranking {
  coin_id    String    @id
  market_cap Decimal?  @db.Decimal(38, 18)
  rank       Int?
  updated_at DateTime? @db.Timestamp(6)
  coins      coins     @relation(fields: [coin_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "market_cap_ranking_coins_fkey")
}

model exchange_ranking {
  exchange_id    String    @id
  trading_volume Decimal?  @db.Decimal(38, 18)
  rank           Int?
  update_at      DateTime? @db.Timestamp(6)
  exchanges      exchanges @relation(fields: [exchange_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "exchange_ranking_exchanges_fkey")
}

model blockchains {
  id             String           @id @default(dbgenerated("gen_random_cuid()"))
  name           String           @unique
  symbol         String           @unique
  chain_id       Int              @unique
  logo_url       String?
  is_testnet     Boolean          @default(false)
  is_active      Boolean          @default(false)
  created_at     DateTime?        @default(now()) @db.Timestamp(6)
  coins_metadata coins_metadata[]
  dex_pair       dex_pair[]
  dex_protocols  dex_protocols[]
  tokens         tokens[]
}

model dex_protocols {
  id                String       @id @unique(map: "dex_protocols_id_pkey") @default(dbgenerated("gen_random_cuid()"))
  name              String
  slug              String
  graphql_url       String?
  blockchain_id     String?
  factory_address   String?
  router_address    String?
  logo_url          String?
  website_url       String?
  decumentation_url String?
  is_active         Boolean      @default(true)
  created_at        DateTime     @default(now()) @db.Timestamp(6)
  updated_at        DateTime     @default(now()) @db.Timestamp(6)
  dex_pair          dex_pair[]
  blockchains       blockchains? @relation(fields: [blockchain_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "dex_protocols_blockchains_id_fkey")
}

model dex_pair {
  id                                String        @id @unique(map: "dex_pair_id_pkey") @default(dbgenerated("gen_random_cuid()"))
  protocol_id                       String
  blockchain_id                     String
  pair_address                      String
  token0_id                         String?
  token1_id                         String?
  pair_symbol                       String
  fee_tier                          Decimal?      @db.Decimal(38, 16)
  is_active                         Boolean       @default(true)
  created_at                        DateTime      @default(now()) @db.Timestamp(6)
  blockchains                       blockchains   @relation(fields: [blockchain_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "dex_pair_blockchains_id_fkey")
  dex_protocols                     dex_protocols @relation(fields: [protocol_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "dex_pair_dex_protocols_id_fkey")
  tokens_dex_pair_token0_idTotokens tokens?       @relation("dex_pair_token0_idTotokens", fields: [token0_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "dex_pair_tokens_id_fk")
  tokens_dex_pair_token1_idTotokens tokens?       @relation("dex_pair_token1_idTotokens", fields: [token1_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "dex_pair_tokens_id_fkey")

  @@unique([protocol_id, blockchain_id, pair_address], map: "dex_pair_protocol_id_pair_address_pkey")
}

model coins_metadata {
  coin_id         String       @id @unique(map: "coins_metadata_coin_id_uindex")
  blockchain_id   String?
  website         String?
  rpc_urls        String[]
  explorer_urls   String[]
  social_urls     String?
  contracts       String?
  supply_data_url String?
  blockchains     blockchains? @relation(fields: [blockchain_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "coins_metadata_blockchains_id_fk")
  coins           coins        @relation(fields: [coin_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "coins_metadata_coins_id_fk")
}

model historical_data_status {
  id       String  @id(map: "historical_data_status_pk") @default(dbgenerated("gen_random_cuid()"))
  exchange String
  coin_id  String
  interval String
  status   Boolean @default(false)

  @@unique([exchange, coin_id, interval], map: "historical_data_status_exchange_coin_id_interval_uindex")
}

enum ExchangeType {
  CEX
  DEX
}

enum ApiResponseFormat {
  JSON
  CSV
  XML
}

enum OfferType {
  STAKING
  FARMING
  LENDING
  LAUNCHPAD
}

enum interval_enum {
  M1  @map("1m")
  M5  @map("5m")
  M15 @map("15m")
  H1  @map("1h")
  H4  @map("4h")
  D1  @map("1d")
}

enum tokentype {
  ERC20
  ERC721
  ERC1155
  BEP20
  SPL
  NATIVE
}
