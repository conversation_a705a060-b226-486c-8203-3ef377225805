
Bybit: https://api.bybit.com

    marketsEndpoint:: /v5/market/instruments-info?category=spot
    tickersEndpoint:: /v5/market/tickers?category=spot
    klineEndpoint:: /v5/market/kline
    tradeEndpoint:: /v5/market/tickers?category=spot
    depthEndpoint:: /v5/market/orderbook

Coinbase Exchange: https://api.exchange.coinbase.com

    marketsEndpoint:: /products
    tickersEndpoint:: /products/stats
    klineEndpoint:: /products/{symbol}/candles
    tradeEndpoint:: /products/stats
    depthEndpoint:: /products/{symbol}/book?level=2

OKX: https://www.okx.com

    marketsEndpoint:: /api/v5/public/instruments?instType=SPOT
    tickersEndpoint:: /api/v5/market/tickers?instType=SPOT
    klineEndpoint:: /api/v5/market/candles
    tradeEndpoint:: /api/v5/market/tickers?instType=SPOT
    depthEndpoint:: /api/v5/market/books

Bitget: https://api.bitget.com

    marketsEndpoint:: /api/v2/spot/public/symbols
    tickersEndpoint:: /api/v2/spot/market/tickers
    klineEndpoint:: /api/v2/spot/market/candles
    tradeEndpoint:: /api/v2/spot/market/fills
    depthEndpoint:: /api/v2/spot/market/orderbook

HTX: https://api.huobi.pro

    marketsEndpoint:: /v1/common/symbols
    tickersEndpoint:: /market/tickers
    klineEndpoint:: /market/history/kline
    tradeEndpoint:: /market/tickers
    depthEndpoint:: /market/depth


You are a senior Node.js backend developer.
I will give you the `BitgetAdapter.js` file which is an exchange adapter class that extends `BaseExchangeAdapter`.
Your task is to create a new adapter for a different exchange (e.g., Binance, Bybit, OKX, KuCoin, etc.) using the exact same class structure, method names, variable names, and return data formats as in `BitgetAdapter.js`.
Requirements:
* The new adapter **must** extend `BaseExchangeAdapter`.
* All methods from `BitgetAdapter.js` must exist in the new adapter:
  >
  >   * `fetchMarkets()`
    * `fetchMarketData()`
    * `fetchHistoricalData(symbol, startTime, endTime)`
    * `fetchRawData(symbols)`
    * `fetchDepthData(symbol, price)`
    * `fetchOrderBook(symbol)`
    * All standardizer methods (`standardizeMarket`, `standardizeMarketData`, `standardizeHistoricalData`, `standardizeRawData`)
    * `processDepth(asks, bids, price)`
    * `_normalizeToMs(input)`
* Do **not** rename any variables, function parameters, or returned keys/structure.
* Replace only the API endpoint logic, request parameters, and response parsing logic so it matches the new exchange’s API format.
* Preserve pagination, throttling, and looping behavior exactly as in the original file.
* Match the same comments and coding style for consistency.
* Keep the `module.exports` statement at the end with the correct adapter class name.