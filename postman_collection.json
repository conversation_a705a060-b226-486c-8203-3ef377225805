{"info": {"name": "Exchange Management API", "description": "API collection for managing cryptocurrency exchanges", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Exchange", "description": "Endpoints for managing exchange information", "item": [{"name": "Submit Exchange", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/v1/exchange/submitExchange", "host": ["{{baseUrl}}"], "path": ["api", "v1", "exchange", "submitExchange"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Binance\",\n  \"type\": \"CEX\",\n  \"logoUrl\": \"exchange-logo-123456789-987654321.png\",\n  \"websiteUrl\": \"https://binance.com\",\n  \"isLaunched\": true,\n  \"launchDate\": 1609459200,\n  \"countryOfRegistration\": \"Malta\",\n  \"legalEntityName\": \"Binance Holdings Limited\",\n  \"apiEndpoint\": \"https://api.binance.com\",\n  \"apiDocsUrl\": \"https://binance-docs.github.io/apidocs\",\n  \"supportsWebSocket\": true,\n  \"supportsRestApi\": true,\n  \"supportedNetworks\": [\"Bitcoin\", \"Ethereum\", \"Binance\"],\n  \"smartContractAddress\": \"\",\n  \"explorerUrl\": \"\",\n  \"tradingPairFormat\": \"BTCUSDT\",\n  \"volume24h\": [\"1000000\"],\n  \"tradingPairsCount\": [\"500\"],\n  \"liquidityInfo\": \"High liquidity across major pairs\",\n  \"fiatSupport\": [\"USD\", \"EUR\", \"GBP\"],\n  \"feeStructure\": \"Maker: 0.1%, Taker: 0.1%\",\n  \"requiresKycAml\": true,\n  \"offers\": [\"STAKING\", \"FARMING\", \"LENDING\", \"LAUNCHPAD\"],\n  \"twitterHandle\": \"binance\",\n  \"telegramUrl\": \"https://t.me/binanceexchange\",\n  \"discordUrl\": \"https://discord.gg/binance\",\n  \"redditUrl\": \"https://reddit.com/r/binance\",\n  \"blogUrl\": \"https://binance.com/blog\",\n  \"supportCenterUrl\": \"https://binance.com/support\",\n  \"licenseInfo\": \"Licensed in multiple jurisdictions\",\n  \"securityAuditUrl\": \"https://binance.com/security\",\n  \"bugBountyUrl\": \"https://bugbounty.binance.com\",\n  \"incidentHistory\": \"No major incidents\",\n  \"kycVendor\": \"Internal KYC system\",\n  \"slug\": \"binance\",\n  \"apiResponseFormat\": \"JSON\",\n  \"pairsEndpoint\": \"https://api.binance.com/api/v3/exchangeInfo\",\n  \"volumeEndpoint\": \"https://api.binance.com/api/v3/ticker/24hr\",\n  \"integrationNotes\": \"RESTful API with WebSocket support\"\n}"}, "description": "Submit a new exchange to the platform. All required fields must be provided according to the validation rules."}}, {"name": "Upload Exchange Icon", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/exchange/uploadIcon", "host": ["{{baseUrl}}"], "path": ["api", "v1", "exchange", "uploadIcon"]}, "body": {"mode": "formdata", "formdata": [{"key": "logoPath", "type": "file", "src": [], "description": "Exchange logo image (PNG, JPEG, JPG only). Must be 300x300 pixels."}]}, "description": "Upload an exchange icon. Supports PNG, JPEG, JPG formats. Image must be 300x300 pixels."}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}